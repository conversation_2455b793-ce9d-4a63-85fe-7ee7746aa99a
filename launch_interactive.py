#!/usr/bin/env python3
"""
Interactive launcher for Kraken Live Trading System.
This script shows configuration options and allows user interaction.
"""

import os
import sys
import json
from rich.console import Console
from rich.panel import Panel
from rich.prompt import Confirm

console = Console()

def display_startup_info():
    """Display startup information."""
    startup_panel = Panel(
        "🚀 [bold cyan]KRAKEN LIVE TRADING SYSTEM[/bold cyan]\n\n"
        "⚠️  [bold red]WARNING: REAL MONEY TRADING![/bold red]\n\n"
        "This system will:\n"
        "• Load your API keys from .env file\n"
        "• Show configuration options for review\n"
        "• Execute real trades on live markets\n"
        "• Use actual cryptocurrency and money\n\n"
        "Press Ctrl+C at any time to stop trading immediately.",
        title="🚨 LIVE TRADING SYSTEM 🚨",
        border_style="red"
    )
    console.print(startup_panel)

def check_env_file():
    """Check if .env file exists and has API keys."""
    if not os.path.exists('.env'):
        console.print("❌ .env file not found!", style="red")
        console.print("Please create the .env file with your API keys first.")
        return False
    
    # Quick check for API keys
    try:
        with open('.env', 'r', encoding='utf-8') as f:
            content = f.read()
            if 'API_KEY_1=' in content and 'API_SECRET_1=' in content:
                console.print("✅ Found .env file with API keys", style="green")
                return True
            else:
                console.print("❌ .env file exists but no API keys found", style="red")
                return False
    except Exception as e:
        console.print(f"❌ Error reading .env file: {e}", style="red")
        return False

def main():
    """Main launcher function."""
    try:
        # Display startup info
        display_startup_info()
        
        # Check .env file
        if not check_env_file():
            console.print("\nPlease check your .env file and try again.")
            return 1
        
        # Ask user how they want to proceed
        console.print("\n📋 [bold]How would you like to proceed?[/bold]")
        console.print("1. Review and modify configuration (recommended)")
        console.print("2. Start with current .env settings")
        console.print("3. Exit")
        
        choice = console.input("\nEnter your choice (1-3): ").strip()
        
        if choice == "3":
            console.print("Exiting...")
            return 0
        elif choice == "2":
            # Launch with auto mode
            console.print("\n🚀 Starting with current .env settings...")
            sys.argv.append('--auto')
        elif choice == "1":
            # Launch with interactive mode (default)
            console.print("\n🔧 Starting with configuration review...")
        else:
            console.print("Invalid choice, starting with configuration review...")
        
        # Import and start the main system
        console.print("Loading system components...")
        
        from main import TradingSystem
        
        # Create and initialize system
        system = TradingSystem()
        
        console.print("🔧 Initializing system...")
        if not system.initialize():
            console.print("❌ System initialization failed", style="red")
            return 1
        
        # Get configuration
        config = system.config_manager.get_config()
        
        # Final confirmation before starting live trading
        console.print("\n" + "="*60)
        console.print("🚨 [bold red]FINAL CONFIRMATION[/bold red] 🚨")
        console.print("="*60)
        console.print("You are about to start LIVE TRADING with REAL MONEY!")
        console.print(f"• Max per trade: ${config['max_dollar_per_trade']}")
        console.print(f"• Strategy: {config['strategy'].title()}")
        console.print(f"• Risk profile: {config['risk_profile'].title()}")
        console.print(f"• API keys: {len(system.config_manager.get_api_keys())}")
        
        if not Confirm.ask("\n🎯 Are you ready to start live trading?", default=False):
            console.print("Trading cancelled by user.")
            return 0
        
        # Start the system
        console.print("\n✅ System initialized successfully!")
        console.print("🎯 Starting live trading...")
        
        if config.get('dashboard_enabled', True):
            system.run_with_dashboard()
        else:
            system.run_headless()
        
        return 0
        
    except KeyboardInterrupt:
        console.print("\n⏹️ Trading stopped by user (Ctrl+C)")
        return 0
    except Exception as e:
        console.print(f"\n❌ System error: {e}", style="red")
        import traceback
        console.print(f"Details: {traceback.format_exc()}", style="dim")
        return 1

if __name__ == "__main__":
    sys.exit(main())
