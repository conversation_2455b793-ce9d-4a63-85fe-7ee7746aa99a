@echo off
REM =====================================================
REM Kraken Live Trading System - Auto Setup & Launch
REM =====================================================
REM 
REM This batch file will:
REM 1. Check Python installation
REM 2. Install all dependencies
REM 3. Create necessary directories
REM 4. Load API keys from .env file
REM 5. Launch the trading system with dashboard
REM
REM ⚠️  WARNING: This system trades with REAL MONEY!
REM

title Kraken Live Trading System - Setup and Launch

echo.
echo ========================================================
echo 🚀 KRAKEN LIVE TRADING SYSTEM - AUTO SETUP
echo ========================================================
echo.
echo ⚠️  WARNING: This system trades with REAL MONEY!
echo    Make sure you have configured your API keys in .env
echo.

REM Check if .env file exists
if not exist ".env" (
    echo ❌ ERROR: .env file not found!
    echo.
    echo Please create the .env file with your API keys first:
    echo 1. Copy .env.example to .env
    echo 2. Edit .env with your 5 Kraken API keys
    echo 3. Run this batch file again
    echo.
    pause
    exit /b 1
)

echo ✅ Found .env configuration file
echo.

REM Check Python installation
echo 🔍 Checking Python installation...
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ ERROR: Python is not installed or not in PATH
    echo.
    echo Please install Python 3.8+ from: https://python.org
    echo Make sure to check "Add Python to PATH" during installation
    echo.
    pause
    exit /b 1
)

python --version
echo ✅ Python is installed
echo.

REM Check if we're in the correct directory
if not exist "main.py" (
    echo ❌ ERROR: main.py not found!
    echo Please run this batch file from the trading system directory
    echo.
    pause
    exit /b 1
)

echo ✅ Found main.py - we're in the correct directory
echo.

REM Create necessary directories
echo 📁 Creating necessary directories...
if not exist "data" mkdir data
if not exist "logs" mkdir logs
if not exist "backups" mkdir backups
echo ✅ Directories created
echo.

REM Install/upgrade pip
echo 🔧 Updating pip...
python -m pip install --upgrade pip --quiet
if errorlevel 1 (
    echo ⚠️  Warning: Could not upgrade pip, continuing anyway...
) else (
    echo ✅ Pip updated successfully
)
echo.

REM Install dependencies
echo 📦 Installing dependencies...
echo This may take a few minutes...
echo.
python -m pip install -r requirements.txt
if errorlevel 1 (
    echo ❌ ERROR: Failed to install dependencies
    echo.
    echo Try running manually: pip install -r requirements.txt
    echo.
    pause
    exit /b 1
)

echo ✅ All dependencies installed successfully
echo.

REM Run system tests
echo 🧪 Running system tests...
python test_system.py
if errorlevel 1 (
    echo ❌ ERROR: System tests failed!
    echo Please check the error messages above
    echo.
    pause
    exit /b 1
)

echo ✅ All system tests passed
echo.

REM Check if system is already configured
if exist "data\system_config.json" (
    echo ✅ Found existing system configuration
    echo.
    
    echo Do you want to:
    echo [1] Start trading with existing configuration
    echo [2] Reconfigure the system
    echo [3] Exit
    echo.
    set /p choice="Enter your choice (1-3): "
    
    if "%choice%"=="1" goto start_trading
    if "%choice%"=="2" goto configure_system
    if "%choice%"=="3" goto end
    
    echo Invalid choice, starting with existing configuration...
    goto start_trading
)

:configure_system
echo.
echo ========================================================
echo 🔧 SYSTEM CONFIGURATION
echo ========================================================
echo.
echo The system will now configure itself using your .env file
echo and prompt you for any additional settings.
echo.

REM Create configuration from .env automatically
echo 📝 Configuring system from .env file...
echo This will automatically load your API keys and settings...
echo.
echo.

:start_trading
echo.
echo ========================================================
echo 🚀 STARTING KRAKEN LIVE TRADING SYSTEM
echo ========================================================
echo.
echo ⚠️  FINAL WARNING: About to start LIVE TRADING with REAL MONEY!
echo.
echo The system will:
echo ✓ Use your 5 Kraken API keys
echo ✓ Execute real trades on live markets
echo ✓ Show real-time dashboard
echo ✓ Trade with actual cryptocurrency
echo.
echo Press Ctrl+C at any time to STOP trading immediately
echo.

set /p confirm="Are you ready to start live trading? (y/N): "
if /i not "%confirm%"=="y" (
    echo Trading cancelled by user
    goto end
)

echo.
echo 🎯 Starting live trading system...
echo 📊 Dashboard will appear in 3 seconds...
echo.
timeout /t 3 /nobreak >nul

REM Start the trading system with interactive configuration
python launch_interactive.py

REM If we get here, the system has stopped
echo.
echo ========================================================
echo ⏹️  TRADING SYSTEM STOPPED
echo ========================================================
echo.
echo The trading system has been stopped.
echo Check the logs directory for detailed information.
echo.

:end
echo.
echo Press any key to exit...
pause >nul
