"""
Real-time terminal dashboard for monitoring trading system.
"""

import time
import os
import threading
from typing import Dict, List
from datetime import datetime
from rich.console import Console
from rich.table import Table
from rich.panel import Panel
from rich.layout import Layout
from rich.live import Live
from rich.text import Text
from rich.progress import Progress, BarColumn, TextColumn
import logging

logger = logging.getLogger(__name__)

class TradingDashboard:
    """Real-time terminal dashboard for the trading system."""
    
    def __init__(self, key_manager, market_watcher, risk_manager, traders):
        """Initialize dashboard."""
        self.key_manager = key_manager
        self.market_watcher = market_watcher
        self.risk_manager = risk_manager
        self.traders = traders
        
        self.console = Console()
        self.is_running = False
        self.update_thread = None
        self.update_interval = 1  # Update every second
        
        # Dashboard state
        self.start_time = time.time()
        self.last_update = time.time()
        
    def start(self):
        """Start the dashboard."""
        if self.is_running:
            return
        
        self.is_running = True
        self.start_time = time.time()
        
        # Clear screen and start live display
        os.system('cls' if os.name == 'nt' else 'clear')
        
        with Live(self._create_layout(), refresh_per_second=1, screen=True) as live:
            while self.is_running:
                try:
                    live.update(self._create_layout())
                    time.sleep(self.update_interval)
                except KeyboardInterrupt:
                    break
                except Exception as e:
                    logger.error(f"Dashboard error: {e}")
                    time.sleep(1)
    
    def stop(self):
        """Stop the dashboard."""
        self.is_running = False
    
    def _create_layout(self) -> Layout:
        """Create the main dashboard layout."""
        layout = Layout()
        
        # Split into header and body
        layout.split_column(
            Layout(name="header", size=3),
            Layout(name="body")
        )
        
        # Split body into left and right
        layout["body"].split_row(
            Layout(name="left"),
            Layout(name="right")
        )
        
        # Split left into top and bottom
        layout["left"].split_column(
            Layout(name="system_status", size=8),
            Layout(name="market_data", size=12),
            Layout(name="trading_activity")
        )
        
        # Split right into sections
        layout["right"].split_column(
            Layout(name="api_keys", size=10),
            Layout(name="risk_metrics", size=8),
            Layout(name="alerts")
        )
        
        # Populate sections
        layout["header"].update(self._create_header())
        layout["system_status"].update(self._create_system_status())
        layout["market_data"].update(self._create_market_data())
        layout["trading_activity"].update(self._create_trading_activity())
        layout["api_keys"].update(self._create_api_keys_panel())
        layout["risk_metrics"].update(self._create_risk_metrics())
        layout["alerts"].update(self._create_alerts_panel())
        
        return layout
    
    def _create_header(self) -> Panel:
        """Create header panel."""
        uptime = time.time() - self.start_time
        uptime_str = f"{int(uptime // 3600):02d}:{int((uptime % 3600) // 60):02d}:{int(uptime % 60):02d}"
        
        header_text = Text()
        header_text.append("🚀 KRAKEN LIVE TRADING SYSTEM", style="bold cyan")
        header_text.append(f" | Uptime: {uptime_str}", style="green")
        header_text.append(f" | Last Update: {datetime.now().strftime('%H:%M:%S')}", style="yellow")
        
        return Panel(header_text, style="blue")
    
    def _create_system_status(self) -> Panel:
        """Create system status panel."""
        # Get system capacity
        capacity = self.key_manager.get_total_capacity()
        
        # Get market summary
        market_summary = self.market_watcher.get_market_summary()
        
        # Get risk summary
        risk_summary = self.risk_manager.get_risk_summary()
        
        table = Table(show_header=False, box=None)
        table.add_column("Metric", style="cyan")
        table.add_column("Value", style="white")
        
        # System metrics
        table.add_row("🔑 Active API Keys", f"{capacity['active_keys']}/{capacity['total_keys']}")
        table.add_row("🏥 Healthy Keys", str(capacity['healthy_keys']))
        table.add_row("📊 Monitored Pairs", str(market_summary.get('total_pairs', 0)))
        table.add_row("🎯 Selected Assets", str(market_summary.get('selected_assets', 0)))
        table.add_row("⚡ Active Traders", str(risk_summary.get('active_traders', 0)))
        table.add_row("🛡️ Risk Level", self._colorize_risk_level(risk_summary.get('risk_level', 'UNKNOWN')))
        
        return Panel(table, title="System Status", border_style="green")
    
    def _create_market_data(self) -> Panel:
        """Create market data panel."""
        selected_assets = self.market_watcher.get_selected_assets()

        table = Table()
        table.add_column("Pair", style="cyan")
        table.add_column("Price", style="white")
        table.add_column("24h Change", style="white")
        table.add_column("Volume", style="white")
        table.add_column("Score", style="yellow")

        if not selected_assets:
            # Show loading message if no data yet
            if not self.market_watcher.is_data_ready():
                table.add_row("Loading...", "🔄", "Loading market data...", "Please wait", "...")
            else:
                table.add_row("No data", "❌", "No assets selected", "Check logs", "0.0")
        else:
            for asset in selected_assets[:8]:  # Show top 8
                pair = asset['pair']
                data = asset['data']
                score = asset['score']

                # Format price
                price = f"${data.get('price', 0):.4f}"

                # Format change with color
                change = data.get('change_24h', 0) * 100
                change_str = f"{change:+.2f}%"
                change_style = "green" if change >= 0 else "red"

                # Format volume
                volume = data.get('volume_24h', 0)
                if volume >= 1000000:
                    volume_str = f"${volume/1000000:.1f}M"
                else:
                    volume_str = f"${volume/1000:.0f}K"

                table.add_row(
                    pair,
                    price,
                    Text(change_str, style=change_style),
                    volume_str,
                    f"{score:.1f}"
                )

        # Add status indicator to title
        title = "Top Market Opportunities"
        if not self.market_watcher.is_data_ready():
            title += " (Loading...)"
        elif not selected_assets:
            title += " (No Data)"

        return Panel(table, title=title, border_style="blue")
    
    def _create_trading_activity(self) -> Panel:
        """Create trading activity panel."""
        table = Table()
        table.add_column("Trader", style="cyan")
        table.add_column("Strategy", style="white")
        table.add_column("Trades", style="white")
        table.add_column("Success Rate", style="white")
        table.add_column("Daily P&L", style="white")
        table.add_column("Status", style="white")
        
        for trader_id, trader in self.traders.items():
            stats = trader.get_performance_stats()
            
            # Format success rate
            success_rate = stats.get('success_rate', 0) * 100
            success_style = "green" if success_rate >= 70 else "yellow" if success_rate >= 50 else "red"
            
            # Format P&L
            daily_pnl = stats.get('daily_pnl', 0)
            pnl_str = f"${daily_pnl:+.2f}"
            pnl_style = "green" if daily_pnl >= 0 else "red"
            
            # Status
            status = "🟢 Active" if stats.get('is_active', False) else "🔴 Inactive"
            
            table.add_row(
                trader_id[:8],  # Truncate trader ID
                stats.get('strategy', 'Unknown'),
                str(stats.get('total_trades', 0)),
                Text(f"{success_rate:.1f}%", style=success_style),
                Text(pnl_str, style=pnl_style),
                status
            )
        
        return Panel(table, title="Trading Activity", border_style="yellow")
    
    def _create_api_keys_panel(self) -> Panel:
        """Create API keys status panel."""
        key_status = self.key_manager.get_key_status()
        
        table = Table()
        table.add_column("Key", style="cyan")
        table.add_column("Tier", style="white")
        table.add_column("Rate Limit", style="white")
        table.add_column("Calls/Min", style="white")
        table.add_column("Health", style="white")
        
        for key_id, status in key_status.items():
            # Health indicator
            health = "🟢 Healthy" if status.get('is_healthy', False) else "🔴 Unhealthy"
            
            # Rate limit usage
            rate_limit = status.get('rate_limit_usage', '0/0')
            
            table.add_row(
                status.get('alias', key_id)[:10],
                status.get('tier', 'Unknown'),
                rate_limit,
                str(status.get('calls_per_minute', 0)),
                health
            )
        
        return Panel(table, title="API Keys Status", border_style="magenta")
    
    def _create_risk_metrics(self) -> Panel:
        """Create risk metrics panel."""
        risk_summary = self.risk_manager.get_risk_summary()
        metrics = risk_summary.get('current_metrics', {})
        limits = risk_summary.get('global_limits', {})
        
        table = Table(show_header=False, box=None)
        table.add_column("Metric", style="cyan")
        table.add_column("Current", style="white")
        table.add_column("Limit", style="yellow")
        
        # Exposure
        exposure = metrics.get('total_exposure', 0)
        exposure_limit = limits.get('max_total_exposure', 0)
        exposure_pct = (exposure / exposure_limit * 100) if exposure_limit > 0 else 0
        
        table.add_row(
            "💰 Total Exposure",
            f"${exposure:.0f}",
            f"${exposure_limit:.0f} ({exposure_pct:.1f}%)"
        )
        
        # Daily P&L
        daily_pnl = metrics.get('daily_pnl', 0)
        daily_limit = limits.get('max_daily_loss', 0)
        pnl_style = "green" if daily_pnl >= 0 else "red"
        
        table.add_row(
            "📈 Daily P&L",
            Text(f"${daily_pnl:+.2f}", style=pnl_style),
            f"${daily_limit:.0f} limit"
        )
        
        # Risk Score
        risk_score = metrics.get('risk_score', 0)
        risk_color = self._get_risk_color(risk_score)
        
        table.add_row(
            "⚠️ Risk Score",
            Text(f"{risk_score:.1f}/100", style=risk_color),
            "< 80 target"
        )
        
        # Active Positions
        positions = metrics.get('active_positions', 0)
        table.add_row("📊 Positions", str(positions), "Monitor")
        
        return Panel(table, title="Risk Metrics", border_style="red")
    
    def _create_alerts_panel(self) -> Panel:
        """Create alerts panel."""
        alerts = self.risk_manager.get_recent_alerts(limit=8)
        
        if not alerts:
            return Panel("No recent alerts", title="System Alerts", border_style="green")
        
        alert_text = Text()
        
        for alert in alerts:
            timestamp = datetime.fromtimestamp(alert['timestamp']).strftime('%H:%M:%S')
            severity = alert['severity']
            message = alert['message']
            
            # Color based on severity
            if severity == 'CRITICAL':
                style = "bold red"
                icon = "🚨"
            elif severity == 'HIGH':
                style = "red"
                icon = "⚠️"
            elif severity == 'MEDIUM':
                style = "yellow"
                icon = "⚡"
            else:
                style = "white"
                icon = "ℹ️"
            
            alert_text.append(f"{icon} {timestamp} ", style="dim")
            alert_text.append(f"{message}\n", style=style)
        
        return Panel(alert_text, title="System Alerts", border_style="red")
    
    def _colorize_risk_level(self, risk_level: str) -> Text:
        """Colorize risk level text."""
        if risk_level == "LOW":
            return Text(risk_level, style="green")
        elif risk_level == "MEDIUM":
            return Text(risk_level, style="yellow")
        elif risk_level == "HIGH":
            return Text(risk_level, style="red")
        elif risk_level == "CRITICAL":
            return Text(risk_level, style="bold red")
        else:
            return Text(risk_level, style="white")
    
    def _get_risk_color(self, risk_score: float) -> str:
        """Get color for risk score."""
        if risk_score < 30:
            return "green"
        elif risk_score < 60:
            return "yellow"
        elif risk_score < 80:
            return "red"
        else:
            return "bold red"
    
    def print_startup_banner(self):
        """Print startup banner."""
        banner = """
╔══════════════════════════════════════════════════════════════════════════════╗
║                        🚀 KRAKEN LIVE TRADING SYSTEM 🚀                      ║
║                                                                              ║
║  Multi-API Key Autonomous Crypto Trading Platform                           ║
║  Real-time Market Analysis • Risk Management • Live Dashboard               ║
║                                                                              ║
║  ⚠️  WARNING: This system trades with REAL MONEY on live markets            ║
║  📊 Monitor all activities through the dashboard                             ║
║  🛑 Press Ctrl+C to stop the system safely                                  ║
╚══════════════════════════════════════════════════════════════════════════════╝
        """
        
        self.console.print(banner, style="bold cyan")
        self.console.print("\n🔄 Initializing system components...\n", style="yellow")
    
    def print_shutdown_message(self):
        """Print shutdown message."""
        self.console.print("\n🛑 System shutdown initiated...", style="bold red")
        self.console.print("💾 Saving state and closing positions...", style="yellow")
        self.console.print("✅ System stopped safely.", style="green")
