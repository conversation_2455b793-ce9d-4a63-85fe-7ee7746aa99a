"""
Trader Agent - Executes trades autonomously using assigned API keys.
"""

import time
import logging
import threading
import random
from typing import Dict, List, Optional, Tuple
from datetime import datetime, timedelta
from enum import Enum

from agents.key_manager import KeyManager
from services.database import DatabaseService
from utils.constants import RISK_PROFILES, STRATEGIES

logger = logging.getLogger(__name__)

class TradeType(Enum):
    BUY = "buy"
    SELL = "sell"

class OrderType(Enum):
    MARKET = "market"
    LIMIT = "limit"
    STOP_LOSS = "stop-loss"
    TAKE_PROFIT = "take-profit"

class TradeStatus(Enum):
    PENDING = "pending"
    EXECUTED = "executed"
    FAILED = "failed"
    CANCELLED = "cancelled"

class Trader:
    """Individual trader agent that executes trades using a specific API key."""
    
    def __init__(self, trader_id: str, key_manager: KeyManager, database: DatabaseService,
                 assigned_key: str, config: Dict):
        """Initialize Trader agent."""
        self.trader_id = trader_id
        self.key_manager = key_manager
        self.database = database
        self.assigned_key = assigned_key
        self.config = config
        
        # Trading state
        self.is_active = False
        self.positions = {}
        self.open_orders = {}
        self.trade_history = []
        
        # Risk management
        self.risk_profile = config.get('risk_profile', 'balanced')
        self.max_trades_per_minute = config.get('max_trades_per_minute', 5)
        self.max_dollar_per_trade = config.get('max_dollar_per_trade', 100.0)
        self.strategy = config.get('strategy', 'scalping')
        
        # Performance tracking
        self.total_trades = 0
        self.successful_trades = 0
        self.total_pnl = 0.0
        self.daily_pnl = 0.0
        self.max_drawdown = 0.0
        
        # Rate limiting for trades
        self.recent_trades = []
        self.lock = threading.Lock()
        
        logger.info(f"Initialized trader {trader_id} with key {assigned_key}")
    
    def start_trading(self):
        """Start the trading agent."""
        self.is_active = True
        logger.info(f"Trader {self.trader_id} started")
    
    def stop_trading(self):
        """Stop the trading agent."""
        self.is_active = False
        logger.info(f"Trader {self.trader_id} stopped")
    
    def can_make_trade(self) -> bool:
        """Check if trader can make a new trade based on rate limits."""
        if not self.is_active:
            return False
        
        # Check API key health
        key_status = self.key_manager.get_key_status(self.assigned_key)
        if not key_status.get('is_healthy', False):
            return False
        
        # Check trade rate limits
        now = time.time()
        recent_trades = [t for t in self.recent_trades if now - t < 60]  # Last minute
        
        if len(recent_trades) >= self.max_trades_per_minute:
            return False
        
        return True
    
    def execute_trade(self, pair: str, trade_type: TradeType, amount: float, 
                     price: float = None, order_type: OrderType = OrderType.MARKET) -> Dict:
        """Execute a trade."""
        if not self.can_make_trade():
            return {'success': False, 'error': 'Cannot make trade - rate limited or inactive'}
        
        # Validate trade parameters
        validation_result = self._validate_trade(pair, trade_type, amount, price)
        if not validation_result['valid']:
            return {'success': False, 'error': validation_result['error']}
        
        try:
            # Prepare order parameters
            order_params = {
                'pair': pair,
                'type': trade_type.value,
                'ordertype': order_type.value,
                'volume': str(amount)
            }
            
            if price and order_type == OrderType.LIMIT:
                order_params['price'] = str(price)
            
            # Execute the order
            success, result = self.key_manager.execute_request(
                self.assigned_key, 'add_order', **order_params
            )
            
            if success:
                # Record successful trade
                trade_data = {
                    'trade_id': result.get('txid', [None])[0],
                    'key_id': self.assigned_key,
                    'pair': pair,
                    'side': trade_type.value,
                    'amount': amount,
                    'price': price or 0,
                    'status': 'executed',
                    'strategy': self.strategy
                }
                
                self.database.record_trade(trade_data)
                self._update_trade_stats(True)
                
                # Update recent trades for rate limiting
                with self.lock:
                    self.recent_trades.append(time.time())
                    self.recent_trades = [t for t in self.recent_trades if time.time() - t < 300]  # Keep 5 minutes
                
                logger.info(f"Trade executed: {trade_type.value} {amount} {pair} at {price}")
                
                return {
                    'success': True,
                    'trade_id': trade_data['trade_id'],
                    'details': trade_data
                }
            else:
                self._update_trade_stats(False)
                logger.error(f"Trade failed: {result}")
                return {'success': False, 'error': result}
                
        except Exception as e:
            self._update_trade_stats(False)
            logger.error(f"Trade execution error: {e}")
            return {'success': False, 'error': str(e)}
    
    def _validate_trade(self, pair: str, trade_type: TradeType, amount: float, price: float) -> Dict:
        """Validate trade parameters against risk limits."""
        # Check amount limits
        trade_value = amount * (price or 1)  # Estimate trade value
        
        if trade_value > self.max_dollar_per_trade:
            return {
                'valid': False,
                'error': f'Trade value ${trade_value:.2f} exceeds limit ${self.max_dollar_per_trade}'
            }
        
        # Check position size limits
        risk_config = RISK_PROFILES.get(self.risk_profile, RISK_PROFILES['balanced'])
        max_position_size = risk_config['max_position_size']
        
        # Get current portfolio value (simplified)
        portfolio_value = self._estimate_portfolio_value()
        max_trade_value = portfolio_value * max_position_size
        
        if trade_value > max_trade_value:
            return {
                'valid': False,
                'error': f'Trade exceeds position size limit: ${trade_value:.2f} > ${max_trade_value:.2f}'
            }
        
        # Check if we have sufficient balance
        balance_check = self._check_sufficient_balance(pair, trade_type, amount, price)
        if not balance_check['sufficient']:
            return {
                'valid': False,
                'error': balance_check['error']
            }
        
        return {'valid': True, 'error': None}

    def _check_sufficient_balance(self, pair: str, trade_type: TradeType, amount: float, price: float) -> Dict:
        """Check if account has sufficient balance for the trade."""
        try:
            # Get current account balance
            success, balance_data = self.key_manager.execute_request(
                self.assigned_key, 'get_account_balance'
            )

            if not success:
                return {
                    'sufficient': False,
                    'error': f'Failed to get account balance: {balance_data}'
                }

            if trade_type == TradeType.BUY:
                # For buy orders, check USD balance
                usd_balance = float(balance_data.get('ZUSD', 0))
                required_usd = amount * (price or 1)  # Estimate required USD

                # Add 1% buffer for fees and slippage
                required_usd *= 1.01

                if usd_balance < required_usd:
                    return {
                        'sufficient': False,
                        'error': f'Insufficient USD balance: ${usd_balance:.2f} < ${required_usd:.2f} required'
                    }

            else:  # SELL order
                # For sell orders, check asset balance
                # Convert Kraken pair to asset name (e.g., XXBTZUSD -> XXBT)

                # Kraken pair to asset mapping
                pair_to_asset = {
                    'XXBTZUSD': 'XXBT',    # Bitcoin
                    'XETHZUSD': 'XETH',    # Ethereum
                    'ADAUSD': 'ADA',       # Cardano
                    'SOLUSD': 'SOL',       # Solana
                    'DOTUSD': 'DOT',       # Polkadot
                    'LINKUSD': 'LINK',     # Chainlink
                    'XLTCZUSD': 'XLTC',    # Litecoin
                    'BCHUSD': 'BCH',       # Bitcoin Cash
                    'XXLMZUSD': 'XXLM',    # Stellar
                    'XXRPZUSD': 'XXRP',    # XRP
                    'UNIUSD': 'UNI',       # Uniswap
                    'AAVEUSD': 'AAVE',     # Aave
                    'MATICUSD': 'MATIC',   # Polygon
                    'ALGOUSD': 'ALGO',     # Algorand
                    'ATOMUSD': 'ATOM',     # Cosmos
                    'AVAXUSD': 'AVAX',     # Avalanche
                    'FILUSD': 'FIL',       # Filecoin
                    'TRXUSD': 'TRX'        # Tron
                }

                kraken_asset = pair_to_asset.get(pair, pair.replace('USD', '').replace('ZUSD', ''))
                asset_balance = float(balance_data.get(kraken_asset, 0))

                # Get display name for error message
                display_asset = kraken_asset.replace('X', '').replace('Z', '') if kraken_asset.startswith('X') else kraken_asset

                if asset_balance < amount:
                    return {
                        'sufficient': False,
                        'error': f'Insufficient {display_asset} balance: {asset_balance:.8f} < {amount:.8f} required'
                    }

            return {'sufficient': True, 'error': None}

        except Exception as e:
            logger.error(f"Error checking balance for {self.trader_id}: {e}")
            return {
                'sufficient': False,
                'error': f'Balance check failed: {str(e)}'
            }
    
    def _estimate_portfolio_value(self) -> float:
        """Get actual portfolio value from Kraken account."""
        try:
            # Get account balance from Kraken
            success, balance_data = self.key_manager.execute_request(
                self.assigned_key, 'get_account_balance'
            )

            if not success:
                logger.warning(f"Failed to get balance for {self.trader_id}: {balance_data}")
                return 1000.0  # Fallback to conservative estimate

            # Calculate total USD value
            total_usd = 0.0

            # Add USD balance directly
            usd_balance = float(balance_data.get('ZUSD', 0))
            total_usd += usd_balance

            # Get current market prices for crypto holdings
            crypto_balances = {}
            for asset, balance in balance_data.items():
                if asset != 'ZUSD' and float(balance) > 0:
                    crypto_balances[asset] = float(balance)

            if crypto_balances:
                # Get current prices for crypto assets
                pairs_to_check = []
                for asset in crypto_balances.keys():
                    # Convert asset name to trading pair (e.g., XXBT -> BTCUSD)
                    if asset.startswith('X'):
                        base_asset = asset[1:]  # Remove X prefix
                    else:
                        base_asset = asset

                    pair = f"{base_asset}USD"
                    pairs_to_check.append(pair)

                if pairs_to_check:
                    success, ticker_data = self.key_manager.execute_request(
                        self.assigned_key, 'get_ticker', pairs_to_check
                    )

                    if success:
                        for asset, balance in crypto_balances.items():
                            # Convert asset name to pair
                            if asset.startswith('X'):
                                base_asset = asset[1:]
                            else:
                                base_asset = asset

                            pair = f"{base_asset}USD"
                            if pair in ticker_data:
                                price = float(ticker_data[pair]['c'][0])  # Last price
                                total_usd += balance * price

            logger.debug(f"Portfolio value for {self.trader_id}: ${total_usd:.2f}")
            return max(total_usd, 50.0)  # Minimum $50 to prevent division by zero

        except Exception as e:
            logger.error(f"Error calculating portfolio value for {self.trader_id}: {e}")
            return 1000.0  # Fallback to conservative estimate
    
    def _update_trade_stats(self, success: bool):
        """Update trading statistics."""
        with self.lock:
            self.total_trades += 1
            if success:
                self.successful_trades += 1
    
    def analyze_opportunity(self, market_data: Dict, pair: str) -> Dict:
        """Analyze a trading opportunity and return recommendation."""
        if not self.is_active:
            return {'action': 'hold', 'confidence': 0, 'reason': 'Trader inactive'}
        
        strategy_config = STRATEGIES.get(self.strategy, STRATEGIES['scalping'])
        risk_config = RISK_PROFILES.get(self.risk_profile, RISK_PROFILES['balanced'])
        
        # Get market data for the pair
        data = market_data.get(pair, {})
        if not data:
            return {'action': 'hold', 'confidence': 0, 'reason': 'No market data'}
        
        # Basic analysis based on strategy
        if self.strategy == 'scalping':
            return self._analyze_scalping_opportunity(data, pair, risk_config)
        elif self.strategy == 'swing':
            return self._analyze_swing_opportunity(data, pair, risk_config)
        elif self.strategy == 'trend':
            return self._analyze_trend_opportunity(data, pair, risk_config)
        elif self.strategy == 'momentum':
            return self._analyze_momentum_opportunity(data, pair, risk_config)
        
        return {'action': 'hold', 'confidence': 0, 'reason': 'Unknown strategy'}
    
    def _analyze_scalping_opportunity(self, data: Dict, pair: str, risk_config: Dict) -> Dict:
        """Analyze scalping opportunity."""
        volatility = data.get('volatility', 0)
        volume = data.get('volume_24h', 0)
        change_24h = data.get('change_24h', 0)
        
        # Scalping prefers moderate volatility and high volume
        if volatility < 0.01:
            return {'action': 'hold', 'confidence': 0, 'reason': 'Volatility too low'}
        
        if volatility > 0.05:
            return {'action': 'hold', 'confidence': 0, 'reason': 'Volatility too high'}
        
        if volume < 5000000:  # $5M minimum for scalping
            return {'action': 'hold', 'confidence': 0, 'reason': 'Volume too low'}
        
        # Simple momentum-based decision
        confidence = min(abs(change_24h) * 100, 80)  # Max 80% confidence
        
        if change_24h > 0.002:  # 0.2% positive change
            return {
                'action': 'buy',
                'confidence': confidence,
                'reason': 'Positive momentum detected',
                'suggested_amount': self._calculate_position_size(data, risk_config)
            }
        elif change_24h < -0.002:  # 0.2% negative change
            return {
                'action': 'sell',
                'confidence': confidence,
                'reason': 'Negative momentum detected',
                'suggested_amount': self._calculate_position_size(data, risk_config)
            }
        
        return {'action': 'hold', 'confidence': 0, 'reason': 'No clear signal'}
    
    def _analyze_swing_opportunity(self, data: Dict, pair: str, risk_config: Dict) -> Dict:
        """Analyze swing trading opportunity."""
        # Simplified swing analysis
        change_24h = data.get('change_24h', 0)
        volatility = data.get('volatility', 0)
        
        if volatility < 0.02:
            return {'action': 'hold', 'confidence': 0, 'reason': 'Insufficient volatility for swing'}
        
        # Look for larger moves
        if change_24h > 0.05:  # 5% gain
            return {
                'action': 'sell',
                'confidence': 60,
                'reason': 'Take profit on large gain',
                'suggested_amount': self._calculate_position_size(data, risk_config)
            }
        elif change_24h < -0.05:  # 5% loss
            return {
                'action': 'buy',
                'confidence': 60,
                'reason': 'Buy the dip',
                'suggested_amount': self._calculate_position_size(data, risk_config)
            }
        
        return {'action': 'hold', 'confidence': 0, 'reason': 'Waiting for larger move'}
    
    def _analyze_trend_opportunity(self, data: Dict, pair: str, risk_config: Dict) -> Dict:
        """Analyze trend following opportunity."""
        # Simplified trend analysis
        change_24h = data.get('change_24h', 0)
        
        # Follow the trend
        if change_24h > 0.03:  # 3% uptrend
            return {
                'action': 'buy',
                'confidence': 50,
                'reason': 'Following uptrend',
                'suggested_amount': self._calculate_position_size(data, risk_config)
            }
        elif change_24h < -0.03:  # 3% downtrend
            return {
                'action': 'sell',
                'confidence': 50,
                'reason': 'Following downtrend',
                'suggested_amount': self._calculate_position_size(data, risk_config)
            }
        
        return {'action': 'hold', 'confidence': 0, 'reason': 'No clear trend'}
    
    def _analyze_momentum_opportunity(self, data: Dict, pair: str, risk_config: Dict) -> Dict:
        """Analyze momentum trading opportunity."""
        volatility = data.get('volatility', 0)
        change_24h = data.get('change_24h', 0)
        volume = data.get('volume_24h', 0)
        
        # Momentum needs high volatility and volume
        if volatility < 0.04 or volume < 2000000:
            return {'action': 'hold', 'confidence': 0, 'reason': 'Insufficient momentum conditions'}
        
        # Strong momentum signals
        if abs(change_24h) > 0.08:  # 8% move
            action = 'buy' if change_24h > 0 else 'sell'
            return {
                'action': action,
                'confidence': 70,
                'reason': 'Strong momentum detected',
                'suggested_amount': self._calculate_position_size(data, risk_config)
            }
        
        return {'action': 'hold', 'confidence': 0, 'reason': 'Momentum not strong enough'}
    
    def _calculate_position_size(self, data: Dict, risk_config: Dict) -> float:
        """Calculate appropriate position size."""
        portfolio_value = self._estimate_portfolio_value()
        max_position_value = portfolio_value * risk_config['max_position_size']
        
        # Adjust for volatility
        volatility = data.get('volatility', 0.05)
        volatility_adjustment = max(0.5, 1 - volatility * 2)  # Reduce size for high volatility
        
        adjusted_position_value = max_position_value * volatility_adjustment
        
        # Convert to amount (simplified)
        price = data.get('price', 1)
        amount = min(adjusted_position_value / price, self.max_dollar_per_trade / price)
        
        return round(amount, 6)  # Round to 6 decimal places
    
    def get_performance_stats(self) -> Dict:
        """Get trader performance statistics."""
        with self.lock:
            success_rate = self.successful_trades / max(self.total_trades, 1)
            
            return {
                'trader_id': self.trader_id,
                'assigned_key': self.assigned_key,
                'is_active': self.is_active,
                'strategy': self.strategy,
                'risk_profile': self.risk_profile,
                'total_trades': self.total_trades,
                'successful_trades': self.successful_trades,
                'success_rate': success_rate,
                'total_pnl': self.total_pnl,
                'daily_pnl': self.daily_pnl,
                'max_drawdown': self.max_drawdown,
                'recent_trades_count': len([t for t in self.recent_trades if time.time() - t < 300])
            }
    
    def update_position(self, pair: str, amount: float, avg_price: float):
        """Update position for a trading pair."""
        with self.lock:
            self.positions[pair] = {
                'amount': amount,
                'avg_price': avg_price,
                'last_updated': time.time()
            }
            
            # Update database
            self.database.update_position(self.assigned_key, pair, amount, avg_price)
    
    def get_positions(self) -> Dict:
        """Get current positions."""
        with self.lock:
            return self.positions.copy()
