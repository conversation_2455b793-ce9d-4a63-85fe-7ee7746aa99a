"""
Market Watcher Agent - Monitors crypto markets and selects trading opportunities.
"""

import time
import logging
import threading
from typing import Dict, List, Tuple, Optional
from datetime import datetime, timedelta
import json
import statistics

from agents.key_manager import KeyManager
from services.database import DatabaseService
from utils.constants import MAJOR_PAIRS, US_RESTRICTED_ASSETS, STRATEGIES

logger = logging.getLogger(__name__)

class MarketWatcher:
    """Monitors crypto markets and identifies trading opportunities."""
    
    def __init__(self, key_manager: KeyManager, database: DatabaseService):
        """Initialize Market Watcher."""
        self.key_manager = key_manager
        self.database = database
        self.is_running = False
        self.update_thread = None
        
        # Market data storage
        self.market_data = {}
        self.price_history = {}
        self.volume_data = {}
        self.volatility_data = {}
        
        # Trading opportunities
        self.selected_assets = []
        self.opportunity_scores = {}
        
        # Configuration
        self.update_interval = 30  # seconds
        self.max_assets = 10
        self.min_volume_24h = 1000000  # $1M minimum daily volume

        self.lock = threading.Lock()

        # Initialize with immediate data load
        self._initial_data_loaded = False
    
    def start_monitoring(self):
        """Start market monitoring."""
        if self.is_running:
            return

        self.is_running = True

        # Load initial data immediately
        logger.info("Loading initial market data...")
        self._update_market_data()
        self._analyze_opportunities()
        self._select_best_assets()
        self._initial_data_loaded = True
        logger.info("Initial market data loaded")

        # Start monitoring thread
        self.update_thread = threading.Thread(target=self._monitoring_loop, daemon=True)
        self.update_thread.start()
        logger.info("Market monitoring started")
    
    def stop_monitoring(self):
        """Stop market monitoring."""
        self.is_running = False
        if self.update_thread:
            self.update_thread.join(timeout=5)
        logger.info("Market monitoring stopped")
    
    def _monitoring_loop(self):
        """Main monitoring loop."""
        while self.is_running:
            try:
                self._update_market_data()
                self._analyze_opportunities()
                self._select_best_assets()
                time.sleep(self.update_interval)
            except Exception as e:
                logger.error(f"Error in monitoring loop: {e}")
                time.sleep(5)  # Short delay on error
    
    def _update_market_data(self):
        """Update market data for all major pairs."""
        # Get a key for market data requests
        key_id = self.key_manager.get_best_key_for_request(cost=1)
        if not key_id:
            logger.warning("No available API key for market data update")
            return
        
        try:
            # Get ticker data for all major pairs
            success, ticker_data = self.key_manager.execute_request(
                key_id, 'get_ticker', MAJOR_PAIRS
            )
            
            if not success:
                logger.error(f"Failed to get ticker data: {ticker_data}")
                return
            
            # Process ticker data
            current_time = time.time()
            
            with self.lock:
                for pair, data in ticker_data.items():
                    # Extract relevant data
                    last_price = float(data['c'][0])  # Last trade price
                    volume_24h = float(data['v'][1])  # 24h volume
                    high_24h = float(data['h'][1])    # 24h high
                    low_24h = float(data['l'][1])     # 24h low
                    
                    # Calculate 24h change
                    open_price = float(data['o'])
                    change_24h = (last_price - open_price) / open_price if open_price > 0 else 0
                    
                    # Calculate volatility (high-low range as percentage of price)
                    volatility = (high_24h - low_24h) / last_price if last_price > 0 else 0
                    
                    # Store current market data
                    self.market_data[pair] = {
                        'price': last_price,
                        'volume_24h': volume_24h,
                        'change_24h': change_24h,
                        'volatility': volatility,
                        'high_24h': high_24h,
                        'low_24h': low_24h,
                        'timestamp': current_time
                    }
                    
                    # Update price history
                    if pair not in self.price_history:
                        self.price_history[pair] = []
                    
                    self.price_history[pair].append({
                        'price': last_price,
                        'timestamp': current_time
                    })
                    
                    # Keep only last 100 price points
                    self.price_history[pair] = self.price_history[pair][-100:]
                    
                    # Store in database
                    self.database.update_market_data(
                        pair, last_price, volume_24h, change_24h, volatility
                    )
            
            logger.debug(f"Updated market data for {len(ticker_data)} pairs")
            
        except Exception as e:
            logger.error(f"Failed to update market data: {e}")
    
    def _analyze_opportunities(self):
        """Analyze market data to identify trading opportunities."""
        with self.lock:
            for pair, data in self.market_data.items():
                # Skip restricted assets
                base_asset = pair.replace('USD', '').replace('EUR', '')
                if base_asset in US_RESTRICTED_ASSETS:
                    continue
                
                # Calculate opportunity score
                score = self._calculate_opportunity_score(pair, data)
                self.opportunity_scores[pair] = score
    
    def _calculate_opportunity_score(self, pair: str, data: Dict) -> float:
        """Calculate opportunity score for a trading pair."""
        score = 0.0
        
        # Volume score (higher volume = better)
        volume_score = min(data['volume_24h'] / 10000000, 1.0) * 25  # Max 25 points
        score += volume_score
        
        # Volatility score (moderate volatility preferred)
        volatility = data['volatility']
        if 0.02 <= volatility <= 0.08:  # 2-8% volatility is ideal
            volatility_score = 25
        elif volatility < 0.02:
            volatility_score = volatility * 1250  # Scale up low volatility
        else:
            volatility_score = max(0, 25 - (volatility - 0.08) * 500)  # Penalize high volatility
        score += volatility_score
        
        # Momentum score (based on recent price movement)
        momentum_score = self._calculate_momentum_score(pair)
        score += momentum_score
        
        # Liquidity score (based on spread estimation)
        liquidity_score = self._estimate_liquidity_score(pair)
        score += liquidity_score
        
        # Trend strength score
        trend_score = self._calculate_trend_score(pair)
        score += trend_score
        
        return score
    
    def _calculate_momentum_score(self, pair: str) -> float:
        """Calculate momentum score based on recent price movements."""
        if pair not in self.price_history or len(self.price_history[pair]) < 5:
            return 0.0
        
        prices = [p['price'] for p in self.price_history[pair][-10:]]
        
        # Calculate short-term momentum
        if len(prices) >= 3:
            recent_change = (prices[-1] - prices[-3]) / prices[-3]
            momentum_score = abs(recent_change) * 1000  # Convert to score
            return min(momentum_score, 20)  # Max 20 points
        
        return 0.0
    
    def _estimate_liquidity_score(self, pair: str) -> float:
        """Estimate liquidity score (simplified)."""
        # For now, use volume as proxy for liquidity
        data = self.market_data.get(pair, {})
        volume = data.get('volume_24h', 0)
        
        # Higher volume generally means better liquidity
        if volume > 50000000:  # $50M+
            return 15
        elif volume > 10000000:  # $10M+
            return 10
        elif volume > 1000000:   # $1M+
            return 5
        else:
            return 0
    
    def _calculate_trend_score(self, pair: str) -> float:
        """Calculate trend strength score."""
        if pair not in self.price_history or len(self.price_history[pair]) < 10:
            return 0.0
        
        prices = [p['price'] for p in self.price_history[pair][-20:]]
        
        # Simple trend calculation using linear regression slope
        n = len(prices)
        x_values = list(range(n))
        
        # Calculate slope
        x_mean = statistics.mean(x_values)
        y_mean = statistics.mean(prices)
        
        numerator = sum((x_values[i] - x_mean) * (prices[i] - y_mean) for i in range(n))
        denominator = sum((x_values[i] - x_mean) ** 2 for i in range(n))
        
        if denominator == 0:
            return 0.0
        
        slope = numerator / denominator
        
        # Convert slope to score (normalize by price)
        trend_strength = abs(slope) / y_mean * 1000
        return min(trend_strength, 15)  # Max 15 points
    
    def _select_best_assets(self):
        """Select the best assets for trading based on opportunity scores."""
        # Sort pairs by opportunity score
        sorted_pairs = sorted(
            self.opportunity_scores.items(),
            key=lambda x: x[1],
            reverse=True
        )
        
        # Select top assets that meet minimum criteria
        selected = []
        for pair, score in sorted_pairs:
            if len(selected) >= self.max_assets:
                break
            
            # Check minimum volume requirement
            data = self.market_data.get(pair, {})
            if data.get('volume_24h', 0) < self.min_volume_24h:
                continue
            
            # Check if asset is not restricted
            base_asset = pair.replace('USD', '').replace('EUR', '')
            if base_asset in US_RESTRICTED_ASSETS:
                continue
            
            selected.append({
                'pair': pair,
                'score': score,
                'data': data
            })
        
        with self.lock:
            self.selected_assets = selected
        
        # Log selection
        if selected:
            logger.info(f"Selected {len(selected)} assets for trading:")
            for asset in selected[:5]:  # Log top 5
                logger.info(f"  {asset['pair']}: score={asset['score']:.2f}")
    
    def get_selected_assets(self) -> List[Dict]:
        """Get currently selected assets for trading."""
        with self.lock:
            return self.selected_assets.copy()
    
    def get_market_data(self, pair: str = None) -> Dict:
        """Get market data for a specific pair or all pairs."""
        with self.lock:
            if pair:
                return self.market_data.get(pair, {})
            return self.market_data.copy()
    
    def get_price_history(self, pair: str, limit: int = 50) -> List[Dict]:
        """Get price history for a pair."""
        with self.lock:
            history = self.price_history.get(pair, [])
            return history[-limit:] if history else []

    def is_data_ready(self) -> bool:
        """Check if initial market data has been loaded."""
        return self._initial_data_loaded and len(self.market_data) > 0
    
    def is_asset_suitable_for_strategy(self, pair: str, strategy: str) -> bool:
        """Check if an asset is suitable for a specific trading strategy."""
        if pair not in self.market_data:
            return False
        
        data = self.market_data[pair]
        strategy_config = STRATEGIES.get(strategy, {})
        
        # Check minimum volume
        min_volume = strategy_config.get('min_volume', 1000000)
        if data.get('volume_24h', 0) < min_volume:
            return False
        
        # Check volatility requirements
        volatility = data.get('volatility', 0)
        
        if strategy == 'scalping':
            # Scalping needs moderate volatility and high volume
            return 0.01 <= volatility <= 0.05 and data.get('volume_24h', 0) > 5000000
        elif strategy == 'swing':
            # Swing trading can handle higher volatility
            return 0.02 <= volatility <= 0.10
        elif strategy == 'trend':
            # Trend following needs clear directional movement
            return volatility >= 0.03
        elif strategy == 'momentum':
            # Momentum needs high volatility and volume
            return volatility >= 0.04 and data.get('volume_24h', 0) > 2000000
        
        return True
    
    def get_market_summary(self) -> Dict:
        """Get market summary statistics."""
        with self.lock:
            if not self.market_data:
                return {}
            
            total_volume = sum(data.get('volume_24h', 0) for data in self.market_data.values())
            avg_volatility = statistics.mean(data.get('volatility', 0) for data in self.market_data.values())
            
            # Count gainers and losers
            gainers = sum(1 for data in self.market_data.values() if data.get('change_24h', 0) > 0)
            losers = sum(1 for data in self.market_data.values() if data.get('change_24h', 0) < 0)
            
            return {
                'total_pairs': len(self.market_data),
                'total_volume_24h': total_volume,
                'avg_volatility': avg_volatility,
                'gainers': gainers,
                'losers': losers,
                'selected_assets': len(self.selected_assets),
                'last_update': max(data.get('timestamp', 0) for data in self.market_data.values()) if self.market_data else 0
            }
