# 🔧 Fix Encoding Error - Quick Solution

## ❌ **The Problem**
Your .env file has special characters that Windows can't read properly. This causes the error:
```
'charmap' codec can't decode byte 0x8f in position 120
```

## ✅ **Quick Fix - 2 Options**

### **Option 1: Automatic Fix (Recommended)**

1. **Double-click:** `fix_env_file.bat`
2. **Choose option 1** to create a clean .env file
3. **Edit the new .env file** with your API keys
4. **Run:** `start_trading.bat` again

### **Option 2: Manual Fix**

1. **Delete the current .env file**
2. **Copy .env.clean to .env:**
   ```cmd
   copy .env.clean .env
   ```
3. **Open .env in Notepad** (not WordPad or other editors)
4. **Replace the placeholder values** with your actual API keys
5. **Save as plain text**
6. **Run:** `start_trading.bat` again

---

## 📝 **How to Edit .env File Correctly**

### **Step 1: Open in Notepad**
- Right-click `.env` file
- Choose "Open with" → "Notepad"
- **DO NOT use Word, WordPad, or other rich text editors**

### **Step 2: Replace Placeholders**
Change these lines:
```env
# WRONG (placeholder):
API_KEY_1=YOUR_FIRST_KRAKEN_API_KEY_HERE
API_SECRET_1=YOUR_FIRST_KRAKEN_API_SECRET_HERE

# CORRECT (your actual keys):
API_KEY_1=your_actual_kraken_api_key_from_kraken_website
API_SECRET_1=your_actual_kraken_api_secret_from_kraken_website
```

### **Step 3: Save Correctly**
- Press Ctrl+S to save
- Make sure it saves as `.env` (not `.env.txt`)
- Use "Save as type: All Files" if needed

---

## 🔑 **Where to Get Your API Keys**

1. **Log into Kraken.com**
2. **Go to:** Settings → API
3. **Click:** "Generate New Key"
4. **Enable permissions:**
   - ✅ Query Funds
   - ✅ Query Open Orders & Trades
   - ✅ Query Closed Orders & Trades
   - ✅ Create & Modify Orders
   - ✅ Cancel Orders
5. **Copy the API Key and API Secret**
6. **Paste into .env file**

---

## ⚠️ **Important Notes**

- **Use only plain text editors** (Notepad, VS Code, etc.)
- **Don't use Word or WordPad** - they add special formatting
- **Save as UTF-8 encoding** if your editor asks
- **Don't add extra spaces** around the = sign
- **Keep the file named exactly `.env`** (no .txt extension)

---

## 🚀 **After Fixing**

Once you've fixed the .env file:

1. **Run:** `start_trading.bat`
2. **The system should now load your API keys automatically**
3. **No more manual input required**
4. **Live trading will start**

---

## 🆘 **Still Having Problems?**

If you're still getting encoding errors:

1. **Delete .env file completely**
2. **Create new file in Notepad:**
   - Open Notepad
   - Copy content from .env.clean
   - Paste into Notepad
   - Add your API keys
   - Save as ".env" (with quotes)
3. **Make sure file is saved as .env not .env.txt**

---

## 📞 **Quick Commands**

```cmd
# Fix automatically
fix_env_file.bat

# Create clean file manually
copy .env.clean .env

# Check if file exists
dir .env

# Open in Notepad
notepad .env
```

**🎯 The key is using plain text editors and avoiding special characters!**
