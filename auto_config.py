#!/usr/bin/env python3
"""
Automatic configuration script for Kraken Live Trading System.
Reads configuration from .env file and sets up the system automatically.
"""

import os
import json
import sys
from typing import Dict, List
from rich.console import Console
from rich.panel import Panel
from rich.table import Table

# Add current directory to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from services.encryption import EncryptionService
from utils.constants import RISK_PROFILES, STRATEGIES

console = Console()

class AutoConfig:
    """Automatic configuration from .env file."""
    
    def __init__(self):
        """Initialize auto configuration."""
        self.env_vars = {}
        self.config = {}
        self.api_keys = []
        self.encryption_service = EncryptionService()
        
    def load_env_file(self) -> bool:
        """Load and parse .env file."""
        try:
            if not os.path.exists('.env'):
                console.print("❌ .env file not found!", style="red")
                return False
            
            with open('.env', 'r') as f:
                for line in f:
                    line = line.strip()
                    if line and not line.startswith('#') and '=' in line:
                        key, value = line.split('=', 1)
                        self.env_vars[key.strip()] = value.strip()
            
            console.print("✅ Loaded .env configuration")
            return True
            
        except Exception as e:
            console.print(f"❌ Error loading .env file: {e}", style="red")
            return False
    
    def validate_api_keys(self) -> bool:
        """Validate and extract API keys from .env."""
        try:
            api_keys_found = 0
            
            for i in range(1, 6):  # Check for 5 API keys
                key_var = f"API_KEY_{i}"
                secret_var = f"API_SECRET_{i}"
                alias_var = f"API_ALIAS_{i}"
                tier_var = f"API_TIER_{i}"
                
                if (key_var in self.env_vars and 
                    secret_var in self.env_vars and
                    self.env_vars[key_var] != f"YOUR_{['FIRST', 'SECOND', 'THIRD', 'FOURTH', 'FIFTH'][i-1]}_KRAKEN_API_KEY_HERE"):
                    
                    api_key_data = {
                        'api_key': self.env_vars[key_var],
                        'api_secret': self.env_vars[secret_var],
                        'alias': self.env_vars.get(alias_var, f"API_Key_{i}"),
                        'tier': self.env_vars.get(tier_var, 'starter')
                    }
                    
                    self.api_keys.append(api_key_data)
                    api_keys_found += 1
            
            if api_keys_found == 0:
                console.print("❌ No valid API keys found in .env file!", style="red")
                console.print("Please replace the placeholder values with your actual Kraken API keys.")
                return False
            
            console.print(f"✅ Found {api_keys_found} valid API keys")
            return True
            
        except Exception as e:
            console.print(f"❌ Error validating API keys: {e}", style="red")
            return False
    
    def create_system_config(self) -> bool:
        """Create system configuration from .env variables."""
        try:
            # Extract trading configuration
            self.config = {
                'max_trades_per_minute': int(self.env_vars.get('MAX_TRADES_PER_MINUTE', 25)),
                'max_dollar_per_trade': float(self.env_vars.get('MAX_DOLLAR_PER_TRADE', 100)),
                'risk_profile': self.env_vars.get('RISK_PROFILE', 'balanced'),
                'strategy': self.env_vars.get('TRADING_STRATEGY', 'scalping'),
                'auto_start': self.env_vars.get('AUTO_START_TRADING', 'true').lower() == 'true',
                'dashboard_enabled': self.env_vars.get('DASHBOARD_ENABLED', 'true').lower() == 'true',
                'logging_level': self.env_vars.get('LOGGING_LEVEL', 'INFO')
            }
            
            # Validate configuration values
            if self.config['risk_profile'] not in RISK_PROFILES:
                console.print(f"⚠️  Invalid risk profile: {self.config['risk_profile']}, using 'balanced'", style="yellow")
                self.config['risk_profile'] = 'balanced'
            
            if self.config['strategy'] not in STRATEGIES:
                console.print(f"⚠️  Invalid strategy: {self.config['strategy']}, using 'scalping'", style="yellow")
                self.config['strategy'] = 'scalping'
            
            console.print("✅ System configuration created")
            return True
            
        except Exception as e:
            console.print(f"❌ Error creating system config: {e}", style="red")
            return False
    
    def encrypt_and_store_keys(self) -> bool:
        """Encrypt and store API keys."""
        try:
            # Get master password from .env or prompt
            master_password = self.env_vars.get('MASTER_PASSWORD')
            
            if not master_password or master_password == 'YourSecureMasterPassword123!':
                console.print("⚠️  Using default master password from .env", style="yellow")
                console.print("For better security, change MASTER_PASSWORD in .env file")
                master_password = 'YourSecureMasterPassword123!'
            
            # Set master password for encryption
            self.encryption_service.set_master_password(master_password)
            
            # Encrypt and store API keys
            encrypted_data = {
                'api_keys': self.api_keys,
                'created_at': str(os.path.getmtime('.env')),
                'version': '1.0'
            }
            
            os.makedirs('data', exist_ok=True)
            success = self.encryption_service.encrypt_to_file(
                encrypted_data, 
                'data/encrypted_keys.dat'
            )
            
            if success:
                console.print("✅ API keys encrypted and stored securely")
                return True
            else:
                console.print("❌ Failed to encrypt API keys", style="red")
                return False
                
        except Exception as e:
            console.print(f"❌ Error encrypting API keys: {e}", style="red")
            return False
    
    def save_system_config(self) -> bool:
        """Save system configuration to file."""
        try:
            os.makedirs('data', exist_ok=True)
            
            with open('data/system_config.json', 'w') as f:
                json.dump(self.config, f, indent=2)
            
            console.print("✅ System configuration saved")
            return True
            
        except Exception as e:
            console.print(f"❌ Error saving system config: {e}", style="red")
            return False
    
    def display_configuration_summary(self):
        """Display configuration summary."""
        console.print("\n📋 Configuration Summary", style="bold cyan")
        
        # API Keys table
        api_table = Table(title="API Keys Configuration")
        api_table.add_column("Alias", style="cyan")
        api_table.add_column("Tier", style="green")
        api_table.add_column("Key Preview", style="yellow")
        
        for key_data in self.api_keys:
            key_preview = key_data['api_key'][:8] + "..." + key_data['api_key'][-4:]
            api_table.add_row(
                key_data['alias'],
                key_data['tier'],
                key_preview
            )
        
        console.print(api_table)
        
        # Trading configuration table
        config_table = Table(title="Trading Configuration")
        config_table.add_column("Setting", style="cyan")
        config_table.add_column("Value", style="green")
        
        config_table.add_row("Max Trades/Minute", str(self.config['max_trades_per_minute']))
        config_table.add_row("Max $/Trade", f"${self.config['max_dollar_per_trade']}")
        config_table.add_row("Risk Profile", self.config['risk_profile'])
        config_table.add_row("Strategy", self.config['strategy'])
        config_table.add_row("Auto Start", str(self.config['auto_start']))
        config_table.add_row("Dashboard", str(self.config['dashboard_enabled']))
        
        console.print(config_table)
    
    def run_auto_config(self) -> bool:
        """Run the complete auto-configuration process."""
        console.print("\n🔧 [bold cyan]Automatic System Configuration[/bold cyan]\n")
        
        # Step 1: Load .env file
        if not self.load_env_file():
            return False
        
        # Step 2: Validate API keys
        if not self.validate_api_keys():
            return False
        
        # Step 3: Create system configuration
        if not self.create_system_config():
            return False
        
        # Step 4: Encrypt and store API keys
        if not self.encrypt_and_store_keys():
            return False
        
        # Step 5: Save system configuration
        if not self.save_system_config():
            return False
        
        # Step 6: Display summary
        self.display_configuration_summary()
        
        console.print("\n✅ [bold green]Automatic configuration completed successfully![/bold green]")
        console.print("\nThe system is now ready for live trading.")
        
        return True

def main():
    """Main entry point for auto configuration."""
    try:
        auto_config = AutoConfig()
        
        if auto_config.run_auto_config():
            console.print("\n🚀 Configuration complete! The system is ready to start trading.")
            return 0
        else:
            console.print("\n❌ Configuration failed. Please check the errors above.", style="red")
            return 1
            
    except KeyboardInterrupt:
        console.print("\n⏹️ Configuration cancelled by user.")
        return 1
    except Exception as e:
        console.print(f"\n❌ Unexpected error: {e}", style="red")
        return 1

if __name__ == "__main__":
    sys.exit(main())
