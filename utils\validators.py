"""
Input validation and compliance checks for the trading system.
"""

import re
import logging
from typing import Dict, List, Optional, Tuple, Union
from datetime import datetime

from utils.constants import US_RESTRICTED_ASSETS, MAJOR_PAIRS, RISK_PROFILES, STRATEGIES

logger = logging.getLogger(__name__)

class ValidationError(Exception):
    """Custom exception for validation errors."""
    pass

class TradingValidator:
    """Validates trading parameters and compliance requirements."""
    
    @staticmethod
    def validate_api_key(api_key: str) -> Tuple[bool, str]:
        """Validate Kraken API key format."""
        if not api_key:
            return False, "API key cannot be empty"
        
        # Kraken API keys are typically 56 characters long
        if len(api_key) < 50:
            return False, "API key too short"
        
        # Should contain only alphanumeric characters and some special chars
        if not re.match(r'^[A-Za-z0-9+/=]+$', api_key):
            return False, "API key contains invalid characters"
        
        return True, "Valid API key format"
    
    @staticmethod
    def validate_api_secret(api_secret: str) -> <PERSON><PERSON>[bool, str]:
        """Validate Kraken API secret format."""
        if not api_secret:
            return False, "API secret cannot be empty"
        
        # Kraken API secrets are typically 88 characters (base64 encoded)
        if len(api_secret) < 80:
            return False, "API secret too short"
        
        # Should be valid base64
        try:
            import base64
            base64.b64decode(api_secret)
        except Exception:
            return False, "API secret is not valid base64"
        
        return True, "Valid API secret format"
    
    @staticmethod
    def validate_trading_pair(pair: str) -> Tuple[bool, str]:
        """Validate trading pair format and availability."""
        if not pair:
            return False, "Trading pair cannot be empty"
        
        # Convert to uppercase
        pair = pair.upper()
        
        # Check if it's a known major pair
        if pair not in MAJOR_PAIRS:
            return False, f"Trading pair {pair} not in supported pairs"
        
        # Check for US restrictions
        # Extract base asset from Kraken pair format (e.g., XXBTZUSD -> BTC)
        base_asset = pair.replace('USD', '').replace('EUR', '').replace('GBP', '').replace('ZUSD', '')
        # Remove Kraken prefixes (X, Z)
        if base_asset.startswith('X') and len(base_asset) > 3:
            base_asset = base_asset[1:]  # Remove X prefix (XXBT -> XBT -> BTC)
        if base_asset.startswith('Z') and len(base_asset) > 3:
            base_asset = base_asset[1:]  # Remove Z prefix

        # Convert common Kraken asset names to standard names
        asset_mapping = {
            'XBT': 'BTC',
            'XLM': 'XLM',
            'XRP': 'XRP',
            'LTC': 'LTC',
            'ETH': 'ETH'
        }
        base_asset = asset_mapping.get(base_asset, base_asset)

        if base_asset in US_RESTRICTED_ASSETS:
            return False, f"Asset {base_asset} is restricted for US users"
        
        return True, "Valid trading pair"
    
    @staticmethod
    def validate_trade_amount(amount: float, pair: str) -> Tuple[bool, str]:
        """Validate trade amount."""
        if amount <= 0:
            return False, "Trade amount must be positive"
        
        # Minimum trade amounts (simplified)
        min_amounts = {
            'BTCUSD': 0.0001,
            'ETHUSD': 0.001,
            'XRPUSD': 1.0,
        }
        
        min_amount = min_amounts.get(pair, 0.00001)
        if amount < min_amount:
            return False, f"Amount {amount} below minimum {min_amount} for {pair}"
        
        # Maximum reasonable amount (safety check)
        if amount > 1000:
            return False, f"Amount {amount} seems unreasonably large"
        
        return True, "Valid trade amount"
    
    @staticmethod
    def validate_price(price: float, pair: str) -> Tuple[bool, str]:
        """Validate price for a trading pair."""
        if price <= 0:
            return False, "Price must be positive"
        
        # Basic price range checks (would normally use real market data)
        price_ranges = {
            'BTCUSD': (1000, 200000),
            'ETHUSD': (100, 10000),
            'XRPUSD': (0.1, 10),
        }
        
        if pair in price_ranges:
            min_price, max_price = price_ranges[pair]
            if not (min_price <= price <= max_price):
                return False, f"Price {price} outside reasonable range {min_price}-{max_price} for {pair}"
        
        return True, "Valid price"
    
    @staticmethod
    def validate_risk_profile(risk_profile: str) -> Tuple[bool, str]:
        """Validate risk profile."""
        if not risk_profile:
            return False, "Risk profile cannot be empty"
        
        if risk_profile.lower() not in RISK_PROFILES:
            return False, f"Invalid risk profile. Must be one of: {list(RISK_PROFILES.keys())}"
        
        return True, "Valid risk profile"
    
    @staticmethod
    def validate_strategy(strategy: str) -> Tuple[bool, str]:
        """Validate trading strategy."""
        if not strategy:
            return False, "Strategy cannot be empty"
        
        if strategy.lower() not in STRATEGIES:
            return False, f"Invalid strategy. Must be one of: {list(STRATEGIES.keys())}"
        
        return True, "Valid strategy"
    
    @staticmethod
    def validate_trade_frequency(trades_per_minute: int) -> Tuple[bool, str]:
        """Validate trade frequency limits."""
        if trades_per_minute <= 0:
            return False, "Trades per minute must be positive"
        
        if trades_per_minute > 60:
            return False, "Trades per minute cannot exceed 60 (rate limit protection)"
        
        return True, "Valid trade frequency"
    
    @staticmethod
    def validate_dollar_amount(amount: float) -> Tuple[bool, str]:
        """Validate dollar amount for trades."""
        if amount <= 0:
            return False, "Dollar amount must be positive"
        
        if amount < 1:
            return False, "Minimum trade amount is $1"
        
        if amount > 10000:
            return False, "Maximum trade amount is $10,000 for safety"
        
        return True, "Valid dollar amount"
    
    @staticmethod
    def validate_position_size(position_size: float, portfolio_value: float, 
                             risk_profile: str) -> Tuple[bool, str]:
        """Validate position size against risk limits."""
        if position_size <= 0:
            return False, "Position size must be positive"
        
        if portfolio_value <= 0:
            return False, "Portfolio value must be positive"
        
        # Get risk profile limits
        risk_config = RISK_PROFILES.get(risk_profile.lower(), RISK_PROFILES['balanced'])
        max_position_ratio = risk_config['max_position_size']
        
        position_ratio = position_size / portfolio_value
        
        if position_ratio > max_position_ratio:
            return False, f"Position size {position_ratio:.2%} exceeds {max_position_ratio:.2%} limit for {risk_profile} profile"
        
        return True, "Valid position size"
    
    @staticmethod
    def validate_stop_loss(entry_price: float, stop_loss_price: float, 
                          trade_type: str) -> Tuple[bool, str]:
        """Validate stop loss price."""
        if entry_price <= 0 or stop_loss_price <= 0:
            return False, "Prices must be positive"
        
        if trade_type.lower() == 'buy':
            if stop_loss_price >= entry_price:
                return False, "Stop loss must be below entry price for buy orders"
        elif trade_type.lower() == 'sell':
            if stop_loss_price <= entry_price:
                return False, "Stop loss must be above entry price for sell orders"
        else:
            return False, "Trade type must be 'buy' or 'sell'"
        
        # Check if stop loss is reasonable (not too tight or too wide)
        price_diff_pct = abs(entry_price - stop_loss_price) / entry_price
        
        if price_diff_pct < 0.005:  # 0.5%
            return False, "Stop loss too tight (< 0.5%)"
        
        if price_diff_pct > 0.20:  # 20%
            return False, "Stop loss too wide (> 20%)"
        
        return True, "Valid stop loss"
    
    @staticmethod
    def validate_take_profit(entry_price: float, take_profit_price: float, 
                           trade_type: str) -> Tuple[bool, str]:
        """Validate take profit price."""
        if entry_price <= 0 or take_profit_price <= 0:
            return False, "Prices must be positive"
        
        if trade_type.lower() == 'buy':
            if take_profit_price <= entry_price:
                return False, "Take profit must be above entry price for buy orders"
        elif trade_type.lower() == 'sell':
            if take_profit_price >= entry_price:
                return False, "Take profit must be below entry price for sell orders"
        else:
            return False, "Trade type must be 'buy' or 'sell'"
        
        # Check if take profit is reasonable
        price_diff_pct = abs(take_profit_price - entry_price) / entry_price
        
        if price_diff_pct < 0.01:  # 1%
            return False, "Take profit too small (< 1%)"
        
        if price_diff_pct > 1.0:  # 100%
            return False, "Take profit too large (> 100%)"
        
        return True, "Valid take profit"
    
    @staticmethod
    def validate_time_range(start_time: datetime, end_time: datetime) -> Tuple[bool, str]:
        """Validate time range."""
        if start_time >= end_time:
            return False, "Start time must be before end time"
        
        # Check if range is reasonable
        time_diff = end_time - start_time
        
        if time_diff.total_seconds() < 60:  # Less than 1 minute
            return False, "Time range too short (< 1 minute)"
        
        if time_diff.days > 365:  # More than 1 year
            return False, "Time range too long (> 1 year)"
        
        return True, "Valid time range"
    
    @staticmethod
    def validate_leverage(leverage: float) -> Tuple[bool, str]:
        """Validate leverage amount."""
        if leverage < 1:
            return False, "Leverage must be at least 1x"
        
        if leverage > 5:  # Conservative limit
            return False, "Leverage cannot exceed 5x for safety"
        
        return True, "Valid leverage"
    
    @staticmethod
    def check_us_compliance(asset: str, user_location: str = "US") -> Tuple[bool, str]:
        """Check if asset is compliant for US users."""
        if user_location.upper() != "US":
            return True, "Non-US user, no restrictions"
        
        asset = asset.upper()
        
        if asset in US_RESTRICTED_ASSETS:
            return False, f"Asset {asset} is restricted for US users"
        
        return True, "Asset is compliant for US users"
    
    @staticmethod
    def validate_order_type(order_type: str) -> Tuple[bool, str]:
        """Validate order type."""
        valid_types = ['market', 'limit', 'stop-loss', 'take-profit', 'stop-loss-limit', 'take-profit-limit']
        
        if order_type.lower() not in valid_types:
            return False, f"Invalid order type. Must be one of: {valid_types}"
        
        return True, "Valid order type"
    
    @staticmethod
    def validate_portfolio_allocation(allocations: Dict[str, float]) -> Tuple[bool, str]:
        """Validate portfolio allocation percentages."""
        if not allocations:
            return False, "Allocations cannot be empty"
        
        total_allocation = sum(allocations.values())
        
        if abs(total_allocation - 1.0) > 0.01:  # Allow 1% tolerance
            return False, f"Total allocation {total_allocation:.2%} must equal 100%"
        
        # Check individual allocations
        for asset, allocation in allocations.items():
            if allocation < 0:
                return False, f"Allocation for {asset} cannot be negative"
            
            if allocation > 0.5:  # 50% max per asset
                return False, f"Allocation for {asset} ({allocation:.2%}) exceeds 50% limit"
        
        return True, "Valid portfolio allocation"

class ComplianceChecker:
    """Checks regulatory compliance for trading activities."""
    
    @staticmethod
    def check_daily_trading_limits(trades_today: int, volume_today: float) -> Tuple[bool, str]:
        """Check daily trading limits."""
        max_daily_trades = 1000
        max_daily_volume = 100000  # $100k
        
        if trades_today > max_daily_trades:
            return False, f"Daily trade limit exceeded: {trades_today} > {max_daily_trades}"
        
        if volume_today > max_daily_volume:
            return False, f"Daily volume limit exceeded: ${volume_today:,.2f} > ${max_daily_volume:,.2f}"
        
        return True, "Within daily limits"
    
    @staticmethod
    def check_position_concentration(positions: Dict[str, float], 
                                   total_portfolio: float) -> Tuple[bool, str]:
        """Check position concentration limits."""
        max_single_position = 0.25  # 25% max per position
        
        for asset, position_value in positions.items():
            concentration = position_value / total_portfolio
            
            if concentration > max_single_position:
                return False, f"Position in {asset} ({concentration:.2%}) exceeds 25% limit"
        
        return True, "Position concentration within limits"
    
    @staticmethod
    def check_risk_metrics(current_drawdown: float, daily_loss: float, 
                         max_drawdown: float = 0.20, max_daily_loss: float = 0.05) -> Tuple[bool, str]:
        """Check risk metrics against limits."""
        if current_drawdown > max_drawdown:
            return False, f"Drawdown {current_drawdown:.2%} exceeds {max_drawdown:.2%} limit"
        
        if daily_loss > max_daily_loss:
            return False, f"Daily loss {daily_loss:.2%} exceeds {max_daily_loss:.2%} limit"
        
        return True, "Risk metrics within acceptable limits"
