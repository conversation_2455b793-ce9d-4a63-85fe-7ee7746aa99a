#!/usr/bin/env python3
"""
Production Setup Script for Kraken Live Trading System
This script helps set up the system for live trading with real money.
"""

import os
import sys
import time
import logging
from rich.console import Console
from rich.panel import Panel
from rich.prompt import Confirm
from rich.table import Table

console = Console()

def print_warning():
    """Print critical warning about real money trading."""
    warning_text = """
⚠️  CRITICAL WARNING - REAL MONEY TRADING ⚠️

This system will trade with REAL MONEY on live markets.
• No test mode or simulation
• All trades execute with actual cryptocurrency
• Potential for significant financial loss
• Use only funds you can afford to lose

By proceeding, you acknowledge:
✓ You understand the risks involved
✓ You have tested with minimal amounts first
✓ You accept full responsibility for any losses
✓ You have proper risk management in place
"""
    
    console.print(Panel(warning_text, style="red", title="⚠️ DANGER ⚠️"))

def check_system_requirements():
    """Check if system meets requirements."""
    console.print("\n🔍 Checking system requirements...")
    
    # Check Python version
    if sys.version_info < (3, 8):
        console.print("❌ Python 3.8+ required", style="red")
        return False
    
    # Check required directories
    required_dirs = ['data', 'logs', 'backups']
    for dir_name in required_dirs:
        if not os.path.exists(dir_name):
            os.makedirs(dir_name, exist_ok=True)
            console.print(f"✅ Created directory: {dir_name}")
    
    # Check dependencies
    try:
        import requests
        import pandas
        import numpy
        import cryptography
        import rich
        console.print("✅ All dependencies installed")
    except ImportError as e:
        console.print(f"❌ Missing dependency: {e}", style="red")
        console.print("Run: pip install -r requirements.txt", style="yellow")
        return False
    
    return True

def validate_api_setup():
    """Validate API key setup."""
    console.print("\n🔑 Validating API setup...")
    
    # Check if config exists
    if not os.path.exists('data/system_config.json'):
        console.print("❌ System not configured. Run: python config.py", style="red")
        return False
    
    # Check if encrypted keys exist
    if not os.path.exists('data/encrypted_keys.dat'):
        console.print("❌ No API keys found. Run: python config.py", style="red")
        return False
    
    console.print("✅ Configuration files found")
    return True

def run_system_tests():
    """Run comprehensive system tests."""
    console.print("\n🧪 Running system tests...")
    
    try:
        import subprocess
        result = subprocess.run([sys.executable, 'test_system.py'], 
                              capture_output=True, text=True, timeout=60)
        
        if result.returncode == 0:
            console.print("✅ All system tests passed")
            return True
        else:
            console.print("❌ System tests failed:", style="red")
            console.print(result.stderr)
            return False
    except Exception as e:
        console.print(f"❌ Test execution failed: {e}", style="red")
        return False

def create_backup():
    """Create system backup before production."""
    console.print("\n💾 Creating system backup...")
    
    try:
        import shutil
        from datetime import datetime
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_dir = f"backups/pre_production_{timestamp}"
        
        # Backup configuration files
        os.makedirs(backup_dir, exist_ok=True)
        
        files_to_backup = [
            'data/system_config.json',
            'data/encrypted_keys.dat',
            'requirements.txt'
        ]
        
        for file_path in files_to_backup:
            if os.path.exists(file_path):
                shutil.copy2(file_path, backup_dir)
        
        console.print(f"✅ Backup created: {backup_dir}")
        return True
    except Exception as e:
        console.print(f"❌ Backup failed: {e}", style="red")
        return False

def display_production_checklist():
    """Display final production checklist."""
    console.print("\n📋 Production Deployment Checklist")
    
    checklist = Table(show_header=True, header_style="bold magenta")
    checklist.add_column("Item", style="cyan")
    checklist.add_column("Status", justify="center")
    checklist.add_column("Description")
    
    items = [
        ("API Keys", "✅", "Kraken API keys configured with trading permissions"),
        ("Risk Limits", "⚠️", "Review max trade amounts and risk profile"),
        ("Balance Check", "⚠️", "Verify sufficient account balance"),
        ("Network", "⚠️", "Ensure stable internet connection"),
        ("Monitoring", "⚠️", "Set up monitoring and alerts"),
        ("Emergency Stop", "✅", "Ctrl+C or dashboard stop button"),
        ("Backup Plan", "✅", "System backup created"),
    ]
    
    for item, status, desc in items:
        checklist.add_row(item, status, desc)
    
    console.print(checklist)

def main():
    """Main production setup process."""
    console.print("\n🚀 [bold cyan]Kraken Live Trading System - Production Setup[/bold cyan]\n")
    
    # Step 1: Show warning
    print_warning()
    
    if not Confirm.ask("\nDo you understand the risks and want to proceed?"):
        console.print("Setup cancelled by user.", style="yellow")
        return
    
    # Step 2: Check requirements
    if not check_system_requirements():
        console.print("\n❌ System requirements not met. Please fix issues and try again.", style="red")
        return
    
    # Step 3: Validate API setup
    if not validate_api_setup():
        console.print("\n❌ API setup incomplete. Please configure system first.", style="red")
        return
    
    # Step 4: Run tests
    if not run_system_tests():
        console.print("\n❌ System tests failed. Please fix issues before production.", style="red")
        return
    
    # Step 5: Create backup
    if not create_backup():
        console.print("\n⚠️ Backup failed, but continuing...", style="yellow")
    
    # Step 6: Final checklist
    display_production_checklist()
    
    # Step 7: Final confirmation
    console.print("\n" + "="*60)
    console.print("🎯 [bold green]SYSTEM READY FOR PRODUCTION[/bold green]")
    console.print("="*60)
    
    if Confirm.ask("\nStart live trading now?"):
        console.print("\n🚀 Starting Kraken Live Trading System...")
        console.print("Use Ctrl+C to stop trading at any time.")
        console.print("Monitor the dashboard for real-time updates.\n")
        
        # Start the main system
        try:
            import subprocess
            subprocess.run([sys.executable, 'main.py'])
        except KeyboardInterrupt:
            console.print("\n⏹️ Trading stopped by user.")
        except Exception as e:
            console.print(f"\n❌ System error: {e}", style="red")
    else:
        console.print("\n📝 To start trading later, run: python main.py")
        console.print("💡 For minimal testing, start with small amounts first.")

if __name__ == "__main__":
    main()
