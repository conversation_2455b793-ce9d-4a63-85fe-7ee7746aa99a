#!/usr/bin/env python3
"""
Deployment script for the Kraken Live Trading System.
This script prepares the system for production deployment.
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path

def create_directory_structure():
    """Create necessary directory structure."""
    directories = [
        "data",
        "logs",
        "backups",
        "agents",
        "services",
        "utils"
    ]

    for directory in directories:
        Path(directory).mkdir(exist_ok=True)

    print("✅ Directory structure created")
    return True

def install_dependencies():
    """Install required Python dependencies."""
    print("📦 Installing dependencies...")
    
    try:
        subprocess.check_call([
            sys.executable, "-m", "pip", "install", "-r", "requirements.txt"
        ])
        print("✅ Dependencies installed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to install dependencies: {e}")
        return False

def run_system_tests():
    """Run system tests to verify installation."""
    print("🧪 Running system tests...")
    
    try:
        result = subprocess.run([sys.executable, "test_system.py"], 
                              capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ All system tests passed")
            return True
        else:
            print("❌ System tests failed:")
            print(result.stdout)
            print(result.stderr)
            return False
    except Exception as e:
        print(f"❌ Failed to run tests: {e}")
        return False

def create_startup_scripts():
    """Create startup scripts for different platforms."""

    # Windows batch script
    windows_script = """@echo off
echo Starting Kraken Live Trading System...
python run.py
pause
"""

    with open("start_trading.bat", "w") as f:
        f.write(windows_script)

    # Linux/Mac shell script
    unix_script = """#!/bin/bash
echo "Starting Kraken Live Trading System..."
python3 run.py
"""

    with open("start_trading.sh", "w") as f:
        f.write(unix_script)

    # Make shell script executable
    try:
        os.chmod("start_trading.sh", 0o755)
    except:
        pass  # Windows doesn't support chmod

    print("✅ Startup scripts created")
    return True

def create_config_template():
    """Create configuration template."""
    if not os.path.exists(".env"):
        shutil.copy(".env.example", ".env")
        print("✅ Configuration template created (.env)")
    else:
        print("ℹ️ Configuration file already exists")
    return True

def display_deployment_summary():
    """Display deployment summary and next steps."""
    
    summary = """
╔══════════════════════════════════════════════════════════════════════════════╗
║                    🚀 DEPLOYMENT COMPLETED SUCCESSFULLY 🚀                   ║
╚══════════════════════════════════════════════════════════════════════════════╝

📋 NEXT STEPS:

1. 🔑 CONFIGURE API KEYS
   • Run: python main.py
   • Follow the setup wizard to add your Kraken API keys
   • Set trading parameters and risk management rules

2. 📊 VERIFY CONFIGURATION
   • Review your settings in the setup wizard
   • Start with conservative parameters for testing
   • Ensure you understand all risks involved

3. 🚀 START TRADING
   • Windows: Double-click start_trading.bat
   • Linux/Mac: ./start_trading.sh
   • Or run: python run.py

4. 📈 MONITOR SYSTEM
   • Use the real-time dashboard to monitor performance
   • Check logs in the logs/ directory
   • Review daily summaries and alerts

⚠️  IMPORTANT WARNINGS:

• This system trades with REAL MONEY on live markets
• Start with small amounts to test the system
• Monitor the system closely, especially initially
• Understand that trading involves substantial risk of loss
• You are responsible for all trading decisions and outcomes

📁 FILE STRUCTURE:
├── main.py              # Main application entry point
├── run.py               # Quick start script
├── config.py            # Configuration management
├── test_system.py       # System tests
├── requirements.txt     # Python dependencies
├── .env                 # Environment configuration
├── agents/              # Trading agents
├── services/            # Core services
├── utils/               # Utility functions
├── data/                # Database and encrypted keys
└── logs/                # System logs

🔗 USEFUL COMMANDS:
• Test system: python test_system.py
• Run setup: python main.py
• Quick start: python run.py
• View logs: tail -f logs/trading_system.log

📞 SUPPORT:
• Read README.md for detailed documentation
• Check logs for troubleshooting
• Ensure API keys have proper permissions

🎯 READY TO TRADE!
Your Kraken Live Trading System is now ready for deployment.
Remember to start with conservative settings and monitor closely.

Good luck and trade responsibly! 🚀
    """
    
    print(summary)

def main():
    """Main deployment function."""
    print("🚀 Kraken Live Trading System - Deployment Script")
    print("=" * 60)
    
    steps = [
        ("Creating directory structure", create_directory_structure),
        ("Installing dependencies", install_dependencies),
        ("Running system tests", run_system_tests),
        ("Creating startup scripts", create_startup_scripts),
        ("Creating configuration template", create_config_template),
    ]
    
    for step_name, step_func in steps:
        print(f"\n📋 {step_name}...")
        if not step_func():
            print(f"❌ Deployment failed at step: {step_name}")
            return False
    
    print("\n" + "=" * 60)
    display_deployment_summary()
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
