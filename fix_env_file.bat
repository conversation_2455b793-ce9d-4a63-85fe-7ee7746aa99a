@echo off
REM =====================================================
REM Fix .env file encoding issues
REM =====================================================

title Fix .env File Encoding

echo.
echo ========================================================
echo 🔧 FIXING .env FILE ENCODING ISSUE
echo ========================================================
echo.
echo The .env file has encoding issues that prevent the system
echo from reading your API keys properly.
echo.

REM Check if .env exists
if not exist ".env" (
    echo ❌ .env file not found!
    echo.
    echo Creating a clean .env file from template...
    copy ".env.clean" ".env"
    if exist ".env" (
        echo ✅ Clean .env file created successfully!
        echo.
        echo Please edit the .env file and add your actual Kraken API keys.
        echo Then run start_trading.bat again.
    ) else (
        echo ❌ Failed to create .env file
    )
    echo.
    pause
    exit /b 0
)

echo Found existing .env file with encoding issues.
echo.
echo Options:
echo [1] Create a clean .env file (recommended)
echo [2] Try to fix the current .env file
echo [3] Exit
echo.
set /p choice="Enter your choice (1-3): "

if "%choice%"=="1" goto create_clean
if "%choice%"=="2" goto fix_current
if "%choice%"=="3" goto end

echo Invalid choice, creating clean file...

:create_clean
echo.
echo 📝 Creating clean .env file...

REM Backup the old file
if exist ".env.backup" del ".env.backup"
ren ".env" ".env.backup"

REM Create clean file
copy ".env.clean" ".env"

if exist ".env" (
    echo ✅ Clean .env file created successfully!
    echo.
    echo Your old .env file has been backed up as .env.backup
    echo.
    echo NEXT STEPS:
    echo 1. Open the new .env file in Notepad
    echo 2. Replace the placeholder API keys with your actual Kraken API keys
    echo 3. Save the file
    echo 4. Run start_trading.bat again
    echo.
    echo Opening .env file in Notepad...
    notepad .env
) else (
    echo ❌ Failed to create clean .env file
)
goto end

:fix_current
echo.
echo 🔧 Attempting to fix current .env file...

REM Create backup
copy ".env" ".env.backup"

REM Try to convert encoding using PowerShell
powershell -Command "Get-Content '.env.backup' | Out-File -FilePath '.env' -Encoding UTF8"

if exist ".env" (
    echo ✅ Attempted to fix encoding
    echo.
    echo Your original file has been backed up as .env.backup
    echo Try running start_trading.bat again.
    echo.
    echo If it still doesn't work, choose option 1 to create a clean file.
) else (
    echo ❌ Failed to fix encoding
    echo Please choose option 1 to create a clean file.
)
goto end

:end
echo.
echo Press any key to exit...
pause >nul
