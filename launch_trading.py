#!/usr/bin/env python3
"""
Simple launcher for Kraken Live Trading System that automatically loads .env configuration.
This script bypasses the interactive setup and uses your .env file directly.
"""

import os
import sys
import json
from rich.console import Console
from rich.panel import Panel

console = Console()

def load_env_config():
    """Load configuration from .env file."""
    if not os.path.exists('.env'):
        console.print("❌ .env file not found!", style="red")
        console.print("Please create the .env file with your API keys first.")
        return False
    
    try:
        env_vars = {}
        # Try different encodings to handle various file formats
        encodings = ['utf-8', 'utf-8-sig', 'latin-1', 'cp1252']

        for encoding in encodings:
            try:
                with open('.env', 'r', encoding=encoding) as f:
                    for line in f:
                        line = line.strip()
                        if line and not line.startswith('#') and '=' in line:
                            key, value = line.split('=', 1)
                            env_vars[key.strip()] = value.strip()
                break  # Success, exit encoding loop
            except UnicodeDecodeError:
                continue  # Try next encoding
        else:
            console.print("❌ Could not read .env file with any encoding", style="red")
            return False
        
        # Check for API keys
        api_keys_found = 0
        for i in range(1, 6):
            key_var = f"API_KEY_{i}"
            if (key_var in env_vars and 
                env_vars[key_var] != f"YOUR_{['FIRST', 'SECOND', 'THIRD', 'FOURTH', 'FIFTH'][i-1]}_KRAKEN_API_KEY_HERE"):
                api_keys_found += 1
        
        if api_keys_found == 0:
            console.print("❌ No valid API keys found in .env file!", style="red")
            console.print("Please edit the .env file and replace the placeholder values with your actual Kraken API keys.")
            return False
        
        console.print(f"✅ Found {api_keys_found} API keys in .env file", style="green")
        
        # Create basic system config
        config = {
            'max_trades_per_minute': int(env_vars.get('MAX_TRADES_PER_MINUTE', 25)),
            'max_dollar_per_trade': float(env_vars.get('MAX_DOLLAR_PER_TRADE', 100)),
            'risk_profile': env_vars.get('RISK_PROFILE', 'balanced'),
            'strategy': env_vars.get('TRADING_STRATEGY', 'scalping'),
            'auto_start': env_vars.get('AUTO_START_TRADING', 'true').lower() == 'true',
            'dashboard_enabled': env_vars.get('DASHBOARD_ENABLED', 'true').lower() == 'true',
            'logging_level': env_vars.get('LOGGING_LEVEL', 'INFO')
        }
        
        # Save basic config
        os.makedirs('data', exist_ok=True)
        with open('data/system_config.json', 'w') as f:
            json.dump(config, f, indent=2)
        
        console.print("✅ Configuration prepared from .env file", style="green")
        return True
        
    except Exception as e:
        console.print(f"❌ Error loading .env file: {e}", style="red")
        return False

def display_startup_info():
    """Display startup information."""
    startup_panel = Panel(
        "🚀 [bold cyan]KRAKEN LIVE TRADING SYSTEM[/bold cyan]\n\n"
        "⚠️  [bold red]WARNING: REAL MONEY TRADING![/bold red]\n\n"
        "This system will:\n"
        "• Load your API keys from .env file\n"
        "• Execute real trades on live markets\n"
        "• Use actual cryptocurrency and money\n\n"
        "Press Ctrl+C at any time to stop trading immediately.",
        title="🚨 LIVE TRADING SYSTEM 🚨",
        border_style="red"
    )
    console.print(startup_panel)

def main():
    """Main launcher function."""
    try:
        # Display startup info
        display_startup_info()
        
        # Load .env configuration
        if not load_env_config():
            console.print("\n❌ Failed to load configuration from .env file", style="red")
            console.print("Please check your .env file and try again.")
            return 1
        
        # Import and start the main system
        console.print("\n🚀 Starting Kraken Live Trading System...")
        console.print("Loading system components...")
        
        # Import main system (this will trigger the auto configuration)
        from main import TradingSystem
        
        # Create and initialize system
        system = TradingSystem()
        
        console.print("🔧 Initializing system...")
        if not system.initialize():
            console.print("❌ System initialization failed", style="red")
            return 1
        
        # Get configuration
        config = system.config_manager.get_config()
        
        # Start the system
        console.print("\n✅ System initialized successfully!")
        console.print("🎯 Starting live trading...")
        
        if config.get('dashboard_enabled', True):
            system.run_with_dashboard()
        else:
            system.run_headless()
        
        return 0
        
    except KeyboardInterrupt:
        console.print("\n⏹️ Trading stopped by user (Ctrl+C)")
        return 0
    except Exception as e:
        console.print(f"\n❌ System error: {e}", style="red")
        return 1

if __name__ == "__main__":
    sys.exit(main())
