# Core HTTP and WebSocket libraries
requests>=2.31.0
websocket-client>=1.6.4
aiohttp>=3.9.5

# Cryptography and security
cryptography>=42.0.0

# Configuration and environment
python-dotenv>=1.0.0

# Data processing and analysis (Python 3.12 compatible)
pandas>=2.2.0
numpy>=1.26.0

# User interface and display
colorama>=0.4.6
rich>=13.7.0

# Task scheduling
schedule>=1.2.0

# Data validation and parsing
pydantic>=2.6.0
python-dateutil>=2.8.2
pytz>=2024.1

# Additional dependencies for trading system
# Note: TA-Lib requires separate C library installation on Windows
# Using alternative technical analysis libraries
scipy>=1.12.0
matplotlib>=3.8.0
# Alternative to TA-Lib for technical indicators
pandas-ta>=0.3.14b0
