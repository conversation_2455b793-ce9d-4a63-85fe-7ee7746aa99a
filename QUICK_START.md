# 🚀 QUICK START - Kraken Live Trading System

## ⚠️ **CRITICAL WARNING**
**THIS SYSTEM TRADES WITH REAL MONEY!** Start with small amounts and understand all risks.

---

## 📋 **3-Step Setup Process**

### **Step 1: Configure Your API Keys**

1. **Open the `.env` file** in any text editor (Notepad, VS Code, etc.)

2. **Replace the placeholder values** with your actual Kraken API keys:

```env
# Replace these with your actual Kraken API keys:
API_KEY_1=YOUR_ACTUAL_KRAKEN_API_KEY_HERE
API_SECRET_1=YOUR_ACTUAL_KRAKEN_API_SECRET_HERE
API_ALIAS_1=Primary_Trading_Key
API_TIER_1=starter

API_KEY_2=YOUR_SECOND_KRAKEN_API_KEY_HERE
API_SECRET_2=YOUR_SECOND_KRAKEN_API_SECRET_HERE
API_ALIAS_2=Secondary_Trading_Key
API_TIER_2=starter

# ... and so on for all 5 keys
```

3. **Set your trading parameters** (optional - defaults are safe):
```env
MAX_TRADES_PER_MINUTE=25
MAX_DOLLAR_PER_TRADE=100
RISK_PROFILE=balanced
TRADING_STRATEGY=scalping
```

4. **Save the `.env` file**

### **Step 2: Run the Setup**

**Double-click:** `start_trading.bat`

**OR run in command prompt:**
```cmd
start_trading.bat
```

The batch file will automatically:
- ✅ Check Python installation
- ✅ Install all dependencies
- ✅ Create necessary directories
- ✅ Load your API keys from .env file automatically
- ✅ Run system tests
- ✅ Launch the trading dashboard

**🎯 NO MANUAL INPUT REQUIRED!** Your .env file contains everything needed.

### **Step 3: Start Trading**

The system will ask for final confirmation before starting live trading:

```
⚠️  FINAL WARNING: About to start LIVE TRADING with REAL MONEY!

Are you ready to start live trading? (y/N):
```

Type `y` and press Enter to start live trading.

---

## 🔑 **Kraken API Key Requirements**

Make sure your Kraken API keys have these permissions:
- ✅ **Query Funds**
- ✅ **Query Open Orders & Trades**
- ✅ **Query Closed Orders & Trades**
- ✅ **Create & Modify Orders**
- ✅ **Cancel Orders**

**How to create Kraken API keys:**
1. Log into your Kraken account
2. Go to Settings → API
3. Click "Generate New Key"
4. Enable the permissions listed above
5. Copy the API Key and API Secret
6. Paste them into the `.env` file

---

## 📊 **What You'll See**

### **Real-Time Dashboard:**
```
🚀 KRAKEN LIVE TRADING SYSTEM
═══════════════════════════════════════

📊 SYSTEM STATUS                    💰 PORTFOLIO
API Keys: 5/5 Healthy              Balance: $2,450.32
Traders: 5 Active                  Daily P&L: +$45.67
Risk Score: 25/100                 Total Trades: 127

📈 LIVE TRADES                      ⚠️  RISK METRICS
BTC/USD: BUY 0.001 @ $43,250      Max Exposure: $500
ETH/USD: SELL 0.05 @ $2,680       Stop Loss: 3%
SOL/USD: BUY 2.5 @ $98.45         Drawdown: 1.2%

🎯 TOP OPPORTUNITIES               📡 MARKET DATA
1. ADA/USD (Score: 85)            BTC: $43,250 (****%)
2. DOT/USD (Score: 78)            ETH: $2,680 (-0.8%)
3. LINK/USD (Score: 72)           SOL: $98.45 (****%)
```

### **Emergency Stop:**
Press `Ctrl+C` at any time to immediately stop all trading.

---

## ⚙️ **Configuration Options**

### **Risk Profiles:**
- **conservative**: 2% max position, 2% stop loss
- **balanced**: 5% max position, 3% stop loss (recommended)
- **aggressive**: 10% max position, 5% stop loss

### **Trading Strategies:**
- **scalping**: High-frequency 1-minute trades
- **swing**: Medium-term 15-minute positions
- **trend**: Long-term 1-hour trend following
- **momentum**: 5-minute momentum trading

### **Safety Limits:**
- **MAX_DOLLAR_PER_TRADE**: Maximum $ amount per trade
- **MAX_TRADES_PER_MINUTE**: Rate limit across all keys
- **MASTER_PASSWORD**: Encrypts your API keys

---

## 🛠️ **Troubleshooting**

### **"Python not found"**
- Install Python 3.8+ from python.org
- Make sure to check "Add Python to PATH"

### **"Dependencies failed to install"**
- Run: `pip install -r requirements.txt`
- Try: `python -m pip install --upgrade pip`

### **"API key validation failed"**
- Check your API keys in .env file
- Verify permissions on Kraken account
- Make sure keys aren't expired

### **"System tests failed"**
- Check internet connection
- Verify Kraken API is accessible
- Try running: `python test_system.py`

---

## 📞 **Quick Commands**

```cmd
# Start the system
start_trading.bat

# Run tests only
python test_system.py

# Manual configuration
python main.py

# Check system status
python -c "import main; print('System ready')"
```

---

## 🎯 **Ready to Start?**

1. **Edit `.env`** with your 5 Kraken API keys
2. **Double-click `start_trading.bat`**
3. **Follow the prompts**
4. **Start with small amounts**
5. **Monitor the dashboard closely**

**⚠️ Remember: Start small, monitor closely, and understand the risks!**

---

## 📋 **Checklist Before Starting**

- [ ] 5 Kraken API keys configured in `.env`
- [ ] API keys have trading permissions
- [ ] Sufficient balance in Kraken account ($500+ recommended)
- [ ] Stable internet connection
- [ ] Understanding of risks involved
- [ ] Emergency stop plan (Ctrl+C)
- [ ] Monitoring plan for first 24 hours

**🚀 You're ready to start live trading!**
