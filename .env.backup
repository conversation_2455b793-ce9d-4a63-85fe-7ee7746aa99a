# Kraken Live Trading System - API Keys Configuration
# =====================================================
# 
# ⚠️  CRITICAL SECURITY WARNING ⚠️
# This file contains your actual API keys that can trade with real money!
# - Keep this file secure and never share it
# - Add .env to .gitignore to prevent accidental commits
# - Use strong passwords and 2FA on your Kraken account
#
# Instructions:
# 1. Replace the placeholder values with your actual Kraken API keys
# 2. Set the tier for each key (starter/intermediate/pro)
# 3. Give each key a unique alias/nickname
# 4. Save this file and run: start_trading.bat
#

# =============================================================================
# API KEY #1 - Primary Trading Key
# =============================================================================
API_KEY_1=TfvThUTT93aMqomDBlHM18ugpfdhIdO9glNTz46KxbPG4kXKICaxRiIc
API_SECRET_1=vrAQd/h34Z+uAqNLIgA/PLJN75OxGLRt1rAf2l2wwuKUzhcY/5piAx1Qbl/riVLFz0+rYEYT9VeJftM3raI/7w==
API_ALIAS_1=Primary_Trading_Key
API_TIER_1=starter

# =============================================================================
# API KEY #2 - Secondary Trading Key  
# =============================================================================
API_KEY_2=OQSFlqaDB7iXarvrfv5j7mt5ojNA2hlMha00i795SrdKqjQ8+yPl2MSQ
API_SECRET_2=LAeSfqCaRscdvZYPWLuma5DeTUfNer+smWg/BW9RHv4PJ5xvRU8sm12aefW2WzNQvTh7MyrPO3qa2CviQ1Seew==
API_ALIAS_2=Secondary_Trading_Key
API_TIER_2=starter

# =============================================================================
# API KEY #3 - Backup Trading Key
# =============================================================================
API_KEY_3=aaXPvIg8VRlHB2XNURywU3rCD9XrgH8myBXffpuemW21FPRhnxwJaiF2
API_SECRET_3=SPjC7BRNsEaW2gSqSjYQ/bRlcuJ9iRuTtanNKUoEit4X8hOOPk3hhskIy8cO1kKgYmf0DnfMUIlvSsDUr8sBBg==
API_ALIAS_3=Backup_Trading_Key
API_TIER_3=starter

# =============================================================================
# API KEY #4 - High Volume Key
# =============================================================================
API_KEY_4=zmjhf3lR+s4zLYpGnRFrMA8h+oEad+tqNh0cReVj8kwW/HEKOysjANUt
API_SECRET_4=ZgKDktIDBf0zpm1bYvuuEAEzq39rC6bDc12B+zw97jMUBL4Y8JD/8QWFnQ55xu8i1k3+5eVHLOzDtCkqe2FPMw==
API_ALIAS_4=High_Volume_Key
API_TIER_4=intermediate

# =============================================================================
# API KEY #5 - Premium Trading Key
# =============================================================================
API_KEY_5=V4FalLksdg1g2JoVNhMQq/wyDx1EVk1bQyL5sYpqxpF2jo1H9rULbM7j
API_SECRET_5=9I57oa+FVfREdp83mz7/fJ0cviy4ButFBEL2RuBQdE+u9f3sTPq57RUzRDHZdEfhe6gDbjLLY1bjqwfud8T7lQ==
API_ALIAS_5=Premium_Trading_Key
API_TIER_5=pro

# =============================================================================
# TRADING CONFIGURATION
# =============================================================================

# Master password for encrypting API keys (change this!)
MASTER_PASSWORD=hero8700!

# Trading Parameters
MAX_TRADES_PER_MINUTE=25
MAX_DOLLAR_PER_TRADE=3
RISK_PROFILE=balanced
TRADING_STRATEGY=scalping

# System Configuration
AUTO_START_TRADING=true
DASHBOARD_ENABLED=true
LOGGING_LEVEL=INFO

# Risk Management
MAX_DAILY_LOSS=500
MAX_TOTAL_EXPOSURE=2000
EMERGENCY_STOP_LOSS=0.05

# =============================================================================
# ADVANCED SETTINGS (Optional)
# =============================================================================

# Database Configuration
DATABASE_PATH=data/trading_system.db
BACKUP_ENABLED=true
BACKUP_INTERVAL_HOURS=6

# Notification Settings (Optional)
WEBHOOK_URL=
EMAIL_NOTIFICATIONS=false
DISCORD_WEBHOOK=

# Development/Testing (Keep these as false for live trading!)
DEBUG_MODE=false
PAPER_TRADING=false
SIMULATION_MODE=false

# =============================================================================
# NOTES:
# =============================================================================
# 
# API Tier Levels:
# - starter: 15 calls per minute
# - intermediate: 20 calls per minute  
# - pro: 20 calls per minute (higher priority)
#
# Risk Profiles:
# - conservative: 2% max position, 2% stop loss
# - balanced: 5% max position, 3% stop loss
# - aggressive: 10% max position, 5% stop loss
#
# Trading Strategies:
# - scalping: 1-minute high-frequency trading
# - swing: 15-minute medium-term positions
# - trend: 1-hour trend following
# - momentum: 5-minute momentum trading
#
# =============================================================================
