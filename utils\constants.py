"""
Constants and configuration values for the Kraken trading system.
"""

# Kraken API Configuration
KRAKEN_API_URL = "https://api.kraken.com"
KRAKEN_WS_URL = "wss://ws.kraken.com"

# Rate Limiting Configuration
RATE_LIMITS = {
    "starter": {"max_counter": 15, "decay_rate": 0.33},
    "intermediate": {"max_counter": 20, "decay_rate": 0.5},
    "pro": {"max_counter": 20, "decay_rate": 1.0}
}

# Trading Configuration
DEFAULT_MAX_TRADES_PER_MINUTE = 10
DEFAULT_MAX_DOLLAR_PER_TRADE = 100.0
DEFAULT_RISK_PROFILE = "balanced"
DEFAULT_STRATEGY = "scalping"

# Risk Profiles
RISK_PROFILES = {
    "conservative": {
        "max_position_size": 0.02,  # 2% of portfolio
        "stop_loss_threshold": 0.02,  # 2% stop loss
        "take_profit_threshold": 0.03,  # 3% take profit
        "volatility_threshold": 0.05  # 5% max volatility
    },
    "balanced": {
        "max_position_size": 0.05,  # 5% of portfolio
        "stop_loss_threshold": 0.03,  # 3% stop loss
        "take_profit_threshold": 0.05,  # 5% take profit
        "volatility_threshold": 0.08  # 8% max volatility
    },
    "aggressive": {
        "max_position_size": 0.10,  # 10% of portfolio
        "stop_loss_threshold": 0.05,  # 5% stop loss
        "take_profit_threshold": 0.08,  # 8% take profit
        "volatility_threshold": 0.15  # 15% max volatility
    }
}

# Trading Strategies
STRATEGIES = {
    "scalping": {
        "timeframe": "1m",
        "indicators": ["rsi", "macd", "bollinger"],
        "min_volume": 1000000,  # $1M daily volume
        "max_spread": 0.001  # 0.1% max spread
    },
    "swing": {
        "timeframe": "15m",
        "indicators": ["ema", "rsi", "volume"],
        "min_volume": 5000000,  # $5M daily volume
        "max_spread": 0.002  # 0.2% max spread
    },
    "trend": {
        "timeframe": "1h",
        "indicators": ["ema", "macd", "adx"],
        "min_volume": 10000000,  # $10M daily volume
        "max_spread": 0.003  # 0.3% max spread
    },
    "momentum": {
        "timeframe": "5m",
        "indicators": ["rsi", "momentum", "volume"],
        "min_volume": 2000000,  # $2M daily volume
        "max_spread": 0.0015  # 0.15% max spread
    }
}

# US Restricted Assets for North Carolina
# Based on Kraken's US restrictions documentation
US_RESTRICTED_ASSETS = [
    "ACA", "AGLD", "ALCH", "ALICE", "ANLOG", "ARC", "ATLAS", "AUDIO", 
    "AVAAI", "BDXN", "COOKIE", "C98", "CLOUD", "CSM", "DBR", "DOLO", 
    "DUCK", "FHE", "GRASS", "HDX", "HMSTR", "INTR", "K", "KERNEL", 
    "KIN", "KMNO", "KOBAN", "L3", "LAYER", "LMWR", "MC", "MV", "NIL", 
    "NMR", "NODL", "NYM", "OMNI", "ORDER", "OXY", "PARA", "PERP", 
    "PORTAL", "PRCL", "PSTAKE", "REQ", "REZ", "ROOK", "SDN", "SPICE", 
    "STEP", "SWARMS", "SWELL", "TEER", "TERM", "VVV", "WAL", "WEN", 
    "WOO", "XRT", "YGG", "ZEX", "XMR", "DASH", "ZEC"
]

# Major trading pairs to monitor (using correct Kraken API pair names)
# All pairs tested and verified working with Kraken API
MAJOR_PAIRS = [
    "XXBTZUSD",   # Bitcoin
    "XETHZUSD",   # Ethereum
    "ADAUSD",     # Cardano
    "SOLUSD",     # Solana
    "DOTUSD",     # Polkadot
    "LINKUSD",    # Chainlink
    "XLTCZUSD",   # Litecoin
    "BCHUSD",     # Bitcoin Cash
    "XXLMZUSD",   # Stellar
    "XXRPZUSD",   # XRP
    "UNIUSD",     # Uniswap
    "AAVEUSD",    # Aave
    "ALGOUSD",    # Algorand
    "ATOMUSD",    # Cosmos
    "AVAXUSD",    # Avalanche
    "FILUSD",     # Filecoin
    "TRXUSD"      # Tron
    # Note: MATICUSD removed as it's not available on Kraken
]

# Database Configuration
DATABASE_PATH = "data/trading_system.db"
LOG_PATH = "logs/"

# Dashboard Update Intervals (seconds)
DASHBOARD_UPDATE_INTERVAL = 1
MARKET_DATA_UPDATE_INTERVAL = 5
PORTFOLIO_UPDATE_INTERVAL = 10

# Notification Settings
WEBHOOK_ENABLED = False
EMAIL_ENABLED = False
DAILY_SUMMARY_TIME = "23:59"

# System Health Thresholds
MAX_API_ERRORS_PER_HOUR = 10
MAX_FAILED_TRADES_PER_HOUR = 5
MIN_ACCOUNT_BALANCE = 50.0  # Minimum USD balance to continue trading
