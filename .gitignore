# Kraken Live Trading System - Git Ignore File
# =============================================

# CRITICAL SECURITY FILES - NEVER COMMIT THESE!
.env
*.env
.env.local
.env.production
data/encrypted_keys.dat
data/system_config.json

# Trading Data and Logs
data/
logs/
backups/
*.db
*.sqlite
*.sqlite3

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
pip-wheel-metadata/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# PyInstaller
*.manifest
*.spec

# Installer logs
pip-log.txt
pip-delete-this-directory.txt

# Unit test / coverage reports
htmlcov/
.tox/
.nox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.py,cover
.hypothesis/
.pytest_cache/

# Virtual environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# IDEs
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Temporary files
*.tmp
*.temp
*.log
*.bak
*.backup

# API Keys and Secrets (extra protection)
*api_key*
*api_secret*
*private_key*
*secret*
*password*
*token*

# Trading specific
trading_history.csv
portfolio_backup.json
emergency_backup.dat
