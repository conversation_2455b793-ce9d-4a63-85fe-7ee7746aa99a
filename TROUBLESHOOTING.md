# 🔧 Troubleshooting Guide - Kraken Live Trading System

## 🚨 Common Errors and Solutions

### **1. "Python not found" or "python is not recognized"**

**Error:**
```
'python' is not recognized as an internal or external command
```

**Solution:**
1. Install Python 3.8+ from [python.org](https://python.org)
2. **IMPORTANT:** Check "Add Python to PATH" during installation
3. Restart your command prompt/terminal
4. Test: `python --version`

**Alternative:**
- Try `python3` instead of `python`
- Try `py` instead of `python`

---

### **2. "No valid API keys found in .env file"**

**Error:**
```
❌ No valid API keys found in .env file!
```

**Solution:**
1. Open `.env` file in any text editor
2. Replace placeholder values with your actual Kraken API keys:
   ```env
   # WRONG (placeholder):
   API_KEY_1=YOUR_FIRST_KRAKEN_API_KEY_HERE
   
   # CORRECT (actual key):
   API_KEY_1=your_actual_api_key_from_kraken
   ```
3. Make sure you have at least one complete API key pair
4. Save the file and try again

---

### **3. "Failed to install dependencies" or NumPy errors**

**Error:**
```
ERROR: Failed building wheel for numpy
```

**Solution:**
1. Update pip first:
   ```cmd
   python -m pip install --upgrade pip
   ```

2. Install dependencies one by one:
   ```cmd
   pip install numpy>=1.26.0
   pip install pandas>=2.2.0
   pip install requests>=2.31.0
   ```

3. If still failing, try:
   ```cmd
   pip install --upgrade setuptools wheel
   pip install -r requirements.txt
   ```

---

### **4. "API key validation failed"**

**Error:**
```
❌ API key validation failed
```

**Possible Causes & Solutions:**

**A. Wrong API Key Format:**
- Kraken API keys are long strings (50+ characters)
- API secrets are even longer (80+ characters)
- Make sure you copied the complete keys

**B. Missing Permissions:**
1. Log into your Kraken account
2. Go to Settings → API
3. Edit your API key
4. Enable these permissions:
   - ✅ Query Funds
   - ✅ Query Open Orders & Trades
   - ✅ Query Closed Orders & Trades
   - ✅ Create & Modify Orders
   - ✅ Cancel Orders

**C. Expired API Keys:**
- Check if your API keys are still active in Kraken
- Create new keys if needed

---

### **5. "System tests failed"**

**Error:**
```
❌ System tests failed
```

**Solution:**
1. Check internet connection
2. Verify Kraken API is accessible:
   ```cmd
   ping api.kraken.com
   ```
3. Run tests manually:
   ```cmd
   python test_system.py
   ```
4. Check the specific error messages

---

### **6. "Configuration setup failed"**

**Error:**
```
❌ Configuration setup failed
```

**Solution:**
1. Delete existing config files:
   ```cmd
   del data\system_config.json
   del data\encrypted_keys.dat
   ```
2. Make sure .env file is properly formatted
3. Try running again:
   ```cmd
   start_trading.bat
   ```

---

### **7. "Rich library" or import errors**

**Error:**
```
ModuleNotFoundError: No module named 'rich'
```

**Solution:**
1. Install missing dependencies:
   ```cmd
   pip install rich colorama pandas numpy requests cryptography
   ```
2. Or reinstall everything:
   ```cmd
   pip install -r requirements.txt
   ```

---

### **8. "Permission denied" or file access errors**

**Error:**
```
PermissionError: [Errno 13] Permission denied
```

**Solution:**
1. Run command prompt as Administrator
2. Or change to a directory you have write access to
3. Make sure antivirus isn't blocking the files

---

### **9. "Rate limit exceeded" during trading**

**Error:**
```
Rate limit exceeded
```

**Solution:**
1. Reduce `MAX_TRADES_PER_MINUTE` in .env file:
   ```env
   MAX_TRADES_PER_MINUTE=10
   ```
2. Add more API keys to distribute load
3. Check your Kraken account tier limits

---

### **10. "Insufficient balance" errors**

**Error:**
```
Insufficient USD balance
```

**Solution:**
1. Add more funds to your Kraken account
2. Reduce `MAX_DOLLAR_PER_TRADE` in .env:
   ```env
   MAX_DOLLAR_PER_TRADE=50
   ```
3. Check your account balance in Kraken

---

## 🔍 **Diagnostic Commands**

### **Check Python Installation:**
```cmd
python --version
pip --version
```

### **Test Dependencies:**
```cmd
python -c "import pandas, numpy, requests, rich; print('All dependencies OK')"
```

### **Test System Components:**
```cmd
python test_system.py
```

### **Check .env File:**
```cmd
python -c "import os; print('✅ .env exists' if os.path.exists('.env') else '❌ .env missing')"
```

### **Manual Configuration:**
```cmd
python main.py
```

---

## 📋 **Pre-Flight Checklist**

Before running the system, verify:

- [ ] Python 3.8+ installed and in PATH
- [ ] All dependencies installed (`pip install -r requirements.txt`)
- [ ] `.env` file exists with your actual API keys
- [ ] API keys have trading permissions in Kraken
- [ ] Sufficient balance in Kraken account
- [ ] Stable internet connection
- [ ] Understanding of risks involved

---

## 🆘 **Emergency Procedures**

### **Stop Trading Immediately:**
- Press `Ctrl+C` in the terminal
- Close the command prompt window
- Check your Kraken account for open positions

### **System Won't Stop:**
- Force close with `Ctrl+Break` or `Ctrl+Z`
- Open Task Manager and end Python processes
- Log into Kraken and manually close positions if needed

### **Lost Money/Unexpected Trades:**
1. Stop the system immediately
2. Check Kraken trade history
3. Review system logs in `logs/` directory
4. Check risk settings in .env file

---

## 📞 **Getting Help**

### **Log Files:**
Check these files for detailed error information:
- `logs/trading_system.log` - Main system log
- `logs/error.log` - Error details

### **System Information:**
When reporting issues, include:
- Python version (`python --version`)
- Operating system (Windows/Linux/Mac)
- Error message (exact text)
- Contents of .env file (without actual API keys)
- Recent log entries

---

## ⚡ **Quick Fixes**

### **"It just won't work":**
1. Delete everything in `data/` folder
2. Edit `.env` with correct API keys
3. Run `start_trading.bat` again

### **"Dependencies keep failing":**
1. Create a new virtual environment:
   ```cmd
   python -m venv trading_env
   trading_env\Scripts\activate
   pip install -r requirements.txt
   ```

### **"API keys not working":**
1. Create new API keys in Kraken
2. Copy them exactly into .env
3. Make sure all permissions are enabled

---

**🚀 Most issues are solved by ensuring your .env file has correct API keys and all dependencies are properly installed!**
