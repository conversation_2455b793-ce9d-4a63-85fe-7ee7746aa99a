"""
Main application entry point for the Kraken Live Trading System.
"""

import sys
import os
import time
import signal
import logging
import threading
from typing import Dict, List
from datetime import datetime

from config import ConfigManager
from services.database import DatabaseService
from services.dashboard import TradingDashboard
from agents.key_manager import KeyManager
from agents.market_watcher import MarketWatcher
from agents.trader import Trader, TradeType
from agents.risk_manager import RiskManager

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/trading_system.log'),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)

class TradingSystem:
    """Main trading system orchestrator."""
    
    def __init__(self):
        """Initialize the trading system."""
        self.config_manager = ConfigManager()
        self.database = None
        self.key_manager = None
        self.market_watcher = None
        self.risk_manager = None
        self.traders: Dict[str, Trader] = {}
        self.dashboard = None
        
        # System state
        self.is_running = False
        self.is_trading = False
        self.shutdown_event = threading.Event()
        
        # Setup signal handlers for graceful shutdown
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)
    
    def _signal_handler(self, signum, frame):
        """Handle shutdown signals."""
        logger.info(f"Received signal {signum}, initiating graceful shutdown...")
        self.shutdown()
    
    def initialize(self) -> bool:
        """Initialize all system components."""
        try:
            logger.info("Initializing Kraken Live Trading System...")
            
            # Setup configuration (check for auto mode)
            auto_mode = os.path.exists('.env') and '--auto' in sys.argv
            if not self.config_manager.setup_system(auto_mode=auto_mode):
                logger.error("Configuration setup failed")
                return False
            
            config = self.config_manager.get_config()
            api_keys = self.config_manager.get_api_keys()
            
            # Initialize database
            self.database = DatabaseService()
            logger.info("Database initialized")
            
            # Initialize key manager
            self.key_manager = KeyManager(self.database)
            
            # Add API keys to key manager
            for key_data in api_keys:
                success = self.key_manager.add_api_key(
                    key_data['api_key'],
                    key_data['api_secret'],
                    key_data['alias'],
                    key_data['tier']
                )
                if not success:
                    logger.error(f"Failed to add API key: {key_data['alias']}")
                    return False
            
            logger.info(f"Added {len(api_keys)} API keys to key manager")
            
            # Initialize market watcher
            self.market_watcher = MarketWatcher(self.key_manager, self.database)
            logger.info("Market watcher initialized")
            
            # Initialize risk manager
            self.risk_manager = RiskManager(self.key_manager, self.database)
            logger.info("Risk manager initialized")
            
            # Initialize traders (one per API key)
            for i, key_data in enumerate(api_keys):
                trader_id = f"trader_{i+1}"
                assigned_key = list(self.key_manager.clients.keys())[i]
                
                trader_config = {
                    'max_trades_per_minute': config['max_trades_per_minute'] // len(api_keys),
                    'max_dollar_per_trade': config['max_dollar_per_trade'],
                    'risk_profile': config['risk_profile'],
                    'strategy': config['strategy']
                }
                
                trader = Trader(
                    trader_id,
                    self.key_manager,
                    self.database,
                    assigned_key,
                    trader_config
                )
                
                self.traders[trader_id] = trader
                self.risk_manager.register_trader(trader)
            
            logger.info(f"Initialized {len(self.traders)} traders")
            
            # Initialize dashboard
            if config.get('dashboard_enabled', True):
                self.dashboard = TradingDashboard(
                    self.key_manager,
                    self.market_watcher,
                    self.risk_manager,
                    self.traders
                )
                logger.info("Dashboard initialized")
            
            self.is_running = True
            logger.info("System initialization completed successfully")
            return True
            
        except Exception as e:
            logger.error(f"System initialization failed: {e}")
            return False
    
    def start_trading(self):
        """Start all trading activities."""
        if not self.is_running:
            logger.error("System not initialized")
            return
        
        try:
            logger.info("Starting trading activities...")
            
            # Start market monitoring
            self.market_watcher.start_monitoring()
            
            # Start risk monitoring
            self.risk_manager.start_monitoring()
            
            # Start traders
            for trader in self.traders.values():
                trader.start_trading()
            
            self.is_trading = True
            logger.info("All trading activities started")
            
            # Start main trading loop
            self._start_trading_loop()
            
        except Exception as e:
            logger.error(f"Failed to start trading: {e}")
            self.stop_trading()
    
    def _start_trading_loop(self):
        """Main trading loop."""
        logger.info("Starting main trading loop...")
        
        while self.is_trading and not self.shutdown_event.is_set():
            try:
                # Get selected assets from market watcher
                selected_assets = self.market_watcher.get_selected_assets()
                
                if not selected_assets:
                    time.sleep(5)  # Wait if no assets selected
                    continue
                
                # Get market data
                market_data = self.market_watcher.get_market_data()
                
                # Process trading opportunities for each trader
                for trader in self.traders.values():
                    if not trader.is_active:
                        continue
                    
                    # Analyze opportunities for each selected asset
                    for asset in selected_assets[:3]:  # Limit to top 3 assets per trader
                        pair = asset['pair']
                        
                        # Check if asset is suitable for trader's strategy
                        if not self.market_watcher.is_asset_suitable_for_strategy(
                            pair, trader.strategy
                        ):
                            continue
                        
                        # Get trading recommendation
                        recommendation = trader.analyze_opportunity(market_data, pair)
                        
                        if recommendation['action'] in ['buy', 'sell'] and recommendation['confidence'] > 60:
                            # Check with risk manager
                            approval = self.risk_manager.approve_trade(
                                trader.trader_id,
                                pair,
                                recommendation['action'],
                                recommendation.get('suggested_amount', 0.01),
                                market_data[pair].get('price', 0)
                            )
                            
                            if approval['approved']:
                                # Execute trade
                                result = trader.execute_trade(
                                    pair,
                                    TradeType.BUY if recommendation['action'] == 'buy' else TradeType.SELL,
                                    recommendation.get('suggested_amount', 0.01)
                                )
                                
                                if result['success']:
                                    logger.info(f"Trade executed: {trader.trader_id} {recommendation['action']} {pair}")
                                else:
                                    logger.warning(f"Trade failed: {result['error']}")
                            else:
                                logger.debug(f"Trade rejected by risk manager: {approval['reason']}")
                
                # Sleep between trading cycles
                time.sleep(1)
                
            except Exception as e:
                logger.error(f"Error in trading loop: {e}")
                time.sleep(5)
    
    def stop_trading(self):
        """Stop all trading activities."""
        if not self.is_trading:
            return
        
        logger.info("Stopping trading activities...")
        
        # Stop traders
        for trader in self.traders.values():
            trader.stop_trading()
        
        # Stop monitoring
        self.market_watcher.stop_monitoring()
        self.risk_manager.stop_monitoring()
        
        self.is_trading = False
        logger.info("Trading activities stopped")
    
    def run_with_dashboard(self):
        """Run system with dashboard."""
        if not self.dashboard:
            logger.error("Dashboard not initialized")
            return
        
        try:
            # Display startup banner
            self.dashboard.print_startup_banner()
            
            # Display configuration summary
            self.config_manager.display_final_summary()
            
            # Start trading in background
            trading_thread = threading.Thread(target=self.start_trading, daemon=True)
            trading_thread.start()
            
            # Wait a moment for trading to start
            time.sleep(2)
            
            # Start dashboard (blocking)
            self.dashboard.start()
            
        except KeyboardInterrupt:
            logger.info("Dashboard interrupted by user")
        except Exception as e:
            logger.error(f"Dashboard error: {e}")
        finally:
            self.shutdown()
    
    def run_headless(self):
        """Run system without dashboard."""
        try:
            logger.info("Starting headless mode...")
            
            # Start trading
            self.start_trading()
            
            # Keep running until shutdown
            while not self.shutdown_event.is_set():
                time.sleep(1)
                
        except KeyboardInterrupt:
            logger.info("Headless mode interrupted by user")
        except Exception as e:
            logger.error(f"Headless mode error: {e}")
        finally:
            self.shutdown()
    
    def shutdown(self):
        """Graceful system shutdown."""
        if not self.is_running:
            return
        
        logger.info("Initiating system shutdown...")
        
        # Signal shutdown
        self.shutdown_event.set()
        
        # Stop trading
        self.stop_trading()
        
        # Stop dashboard
        if self.dashboard:
            self.dashboard.stop()
            self.dashboard.print_shutdown_message()
        
        # Cleanup database connections
        if self.database:
            self.database.cleanup_old_data()
        
        self.is_running = False
        logger.info("System shutdown completed")
    
    def get_system_status(self) -> Dict:
        """Get current system status."""
        if not self.is_running:
            return {'status': 'not_initialized'}
        
        return {
            'status': 'running' if self.is_trading else 'stopped',
            'traders': len(self.traders),
            'active_traders': len([t for t in self.traders.values() if t.is_active]),
            'api_keys': len(self.key_manager.clients) if self.key_manager else 0,
            'uptime': time.time() - (self.dashboard.start_time if self.dashboard else time.time())
        }

def main():
    """Main entry point."""
    # Create logs directory
    import os
    os.makedirs('logs', exist_ok=True)
    
    # Initialize system
    system = TradingSystem()
    
    if not system.initialize():
        logger.error("System initialization failed")
        sys.exit(1)
    
    # Get configuration
    config = system.config_manager.get_config()
    
    # Run system
    if config.get('dashboard_enabled', True):
        system.run_with_dashboard()
    else:
        system.run_headless()

if __name__ == "__main__":
    main()
