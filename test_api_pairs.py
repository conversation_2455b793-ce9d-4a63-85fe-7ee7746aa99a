#!/usr/bin/env python3
"""
Test script to verify Kraken API pair names are working correctly.
"""

import requests
import json
from utils.constants import MAJOR_PAIRS

def test_kraken_pairs():
    """Test if our configured pairs work with Kraken API."""
    print("🔍 Testing Kraken API pair names...")
    print(f"Configured pairs: {len(MAJOR_PAIRS)}")
    
    # Test each pair individually to identify problematic ones
    working_pairs = []
    failed_pairs = []
    
    for pair in MAJOR_PAIRS:
        try:
            print(f"Testing {pair}...", end=" ")
            
            url = 'https://api.kraken.com/0/public/Ticker'
            params = {'pair': pair}
            response = requests.get(url, params=params, timeout=10)
            data = response.json()
            
            if 'error' in data and data['error']:
                print(f"❌ Error: {data['error']}")
                failed_pairs.append((pair, data['error']))
            elif 'result' in data and data['result']:
                print("✅ OK")
                working_pairs.append(pair)
            else:
                print("❓ Unexpected response")
                failed_pairs.append((pair, "Unexpected response"))
                
        except Exception as e:
            print(f"❌ Exception: {e}")
            failed_pairs.append((pair, str(e)))
    
    print(f"\n📊 Results:")
    print(f"✅ Working pairs: {len(working_pairs)}")
    print(f"❌ Failed pairs: {len(failed_pairs)}")
    
    if working_pairs:
        print(f"\n✅ Working pairs:")
        for pair in working_pairs:
            print(f"  {pair}")
    
    if failed_pairs:
        print(f"\n❌ Failed pairs:")
        for pair, error in failed_pairs:
            print(f"  {pair}: {error}")
    
    return working_pairs, failed_pairs

def get_correct_pair_names():
    """Get all available USD pairs from Kraken to find correct names."""
    print("\n🔍 Getting all available USD pairs from Kraken...")
    
    try:
        response = requests.get('https://api.kraken.com/0/public/AssetPairs', timeout=15)
        data = response.json()
        
        if 'result' in data:
            pairs = data['result']
            
            # Filter for USD pairs (excluding USDT and USDC)
            usd_pairs = []
            for pair_name in pairs.keys():
                if ('USD' in pair_name and 
                    'USDT' not in pair_name and 
                    'USDC' not in pair_name and
                    len(pair_name) <= 12):  # Reasonable length
                    usd_pairs.append(pair_name)
            
            print(f"Found {len(usd_pairs)} USD pairs")
            
            # Look for major crypto pairs
            major_cryptos = ['BTC', 'ETH', 'ADA', 'SOL', 'DOT', 'LINK', 'LTC', 'BCH', 'XLM', 'XRP', 'UNI', 'AAVE']
            
            suggested_pairs = []
            for crypto in major_cryptos:
                matches = [p for p in usd_pairs if crypto in p or f'X{crypto}' in p]
                if matches:
                    # Prefer the simplest name
                    best_match = min(matches, key=len)
                    suggested_pairs.append((crypto, best_match))
            
            print(f"\n💡 Suggested pair mappings:")
            for crypto, pair in suggested_pairs:
                print(f"  {crypto}: {pair}")
            
            return suggested_pairs
            
    except Exception as e:
        print(f"Error getting pair names: {e}")
        return []

if __name__ == "__main__":
    print("🚀 Kraken API Pair Testing\n")
    
    # Test current configuration
    working, failed = test_kraken_pairs()
    
    # If we have failures, get suggestions
    if failed:
        suggestions = get_correct_pair_names()
        
        if suggestions:
            print(f"\n📝 To fix the errors, update MAJOR_PAIRS in utils/constants.py:")
            print("MAJOR_PAIRS = [")
            for crypto, pair in suggestions:
                print(f'    "{pair}",  # {crypto}')
            print("]")
    
    print(f"\n🎯 Summary:")
    if len(working) >= 10:
        print("✅ System should work well with current configuration")
    elif len(working) >= 5:
        print("⚠️ System will work but with limited pairs")
    else:
        print("❌ System needs pair name fixes to work properly")
